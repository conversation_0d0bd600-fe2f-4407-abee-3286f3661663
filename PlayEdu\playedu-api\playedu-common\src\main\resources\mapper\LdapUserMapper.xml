<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.common.mapper.LdapUserMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.common.domain.LdapUser">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="INTEGER"/>
            <result property="cn" column="cn" jdbcType="VARCHAR"/>
            <result property="dn" column="dn" jdbcType="VARCHAR"/>
            <result property="ou" column="ou" jdbcType="VARCHAR"/>
            <result property="uid" column="uid" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,uuid,user_id,
        cn,dn,ou,
        uid,email,created_at,
        updated_at
    </sql>
</mapper>
