import { useState, useEffect } from "react";
import { Modal, Form, Input, Button, Space, message, Progress, Select, Alert } from "antd";
import { UploadOutlined, FileZipOutlined } from "@ant-design/icons";
import { upload, resourceCategory } from "../../api/index";
import styles from "./index.module.less";

const { Option } = Select;
const { TextArea } = Input;

interface PropInterface {
  onUpdate: () => void;
  onCancel: () => void;
  open: boolean;
}

export const UploadScormSub: React.FC<PropInterface> = ({ onUpdate, onCancel, open }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [parseProgress, setParseProgress] = useState<number>(0);
  const [isParsing, setIsParsing] = useState<boolean>(false);

  useEffect(() => {
    if (open) {
      getCategories();
    }
  }, [open]);

  const getCategories = () => {
    resourceCategory.resourceCategoryList().then((res: any) => {
      const categoryList = transformCategories(res.data.categories);
      setCategories(categoryList);
    });
  };

  const transformCategories = (categories: any, parentId = 0, level = 0): any[] => {
    const result: any[] = [];
    if (categories[parentId]) {
      categories[parentId].forEach((item: any) => {
        result.push({
          ...item,
          level,
          label: "　".repeat(level) + item.name,
        });
        result.push(...transformCategories(categories, item.id, level + 1));
      });
    }
    return result;
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 检查文件类型
    if (!file.name.toLowerCase().endsWith('.zip')) {
      message.error("只支持 ZIP 格式的SCORM包");
      return;
    }

    // 检查文件大小 (限制为500MB)
    if (file.size > 500 * 1024 * 1024) {
      message.error("SCORM包大小不能超过500MB");
      return;
    }

    setSelectedFile(file);
    form.setFieldsValue({
      name: file.name.replace(/\.[^/.]+$/, ""), // 去掉扩展名
    });
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      message.error("请选择SCORM包文件");
      return;
    }

    try {
      await form.validateFields();
      const values = form.getFieldsValue();

      setIsUploading(true);
      setUploadProgress(0);

      // 创建FormData
      const formData = new FormData();
      formData.append("file", selectedFile);
      formData.append("name", values.name);
      formData.append("description", values.description || "");
      formData.append("category_ids", values.category_ids?.join(",") || "");

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + Math.random() * 10;
        });
      }, 200);

      // 执行上传
      const response = await upload.uploadScorm(formData);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      setIsUploading(false);

      // 开始解析
      setIsParsing(true);
      setParseProgress(0);
      
      const parseInterval = setInterval(() => {
        setParseProgress((prev) => {
          if (prev >= 90) {
            clearInterval(parseInterval);
            return prev;
          }
          return prev + Math.random() * 15;
        });
      }, 300);

      // 等待解析完成
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      clearInterval(parseInterval);
      setParseProgress(100);
      setIsParsing(false);

      setTimeout(() => {
        setUploadProgress(0);
        setParseProgress(0);
        setSelectedFile(null);
        form.resetFields();
        message.success("SCORM包上传并解析成功");
        onUpdate();
      }, 500);

    } catch (error) {
      setIsUploading(false);
      setIsParsing(false);
      setUploadProgress(0);
      setParseProgress(0);
      message.error("上传失败，请重试");
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const isProcessing = isUploading || isParsing;

  return (
    <Modal
      title="上传SCORM包"
      open={open}
      onCancel={() => {
        if (!isProcessing) {
          onCancel();
          setSelectedFile(null);
          form.resetFields();
        }
      }}
        footer={null}
        width={600}
        maskClosable={!isProcessing}
        closable={!isProcessing}
      >
        <Alert
          message="SCORM包要求"
          description="请上传符合SCORM 1.2或SCORM 2004标准的ZIP包，包含imsmanifest.xml文件"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpload}
        >
          <Form.Item
            label="选择SCORM包"
            required
          >
            <div className={styles["upload-area"]}>
              <input
                type="file"
                accept=".zip"
                onChange={handleFileSelect}
                style={{ display: "none" }}
                id="scorm-upload-input"
                disabled={isProcessing}
              />
              <label
                htmlFor="scorm-upload-input"
                className={styles["upload-label"]}
              >
                <div className={styles["upload-content"]}>
                  <FileZipOutlined style={{ fontSize: "48px", color: "#1890ff" }} />
                  <div className={styles["upload-text"]}>
                    <div>点击选择SCORM包文件</div>
                    <div className={styles["upload-hint"]}>
                      支持 ZIP 格式，文件大小不超过500MB<br/>
                      必须包含 imsmanifest.xml 文件
                    </div>
                  </div>
                </div>
              </label>
            </div>

            {selectedFile && (
              <div className={styles["file-info"]}>
                <div className={styles["file-details"]}>
                  <FileZipOutlined style={{ color: "#1890ff", marginRight: "8px" }} />
                  <span className={styles["file-name"]}>{selectedFile.name}</span>
                  <span className={styles["file-size"]}>
                    ({formatFileSize(selectedFile.size)})
                  </span>
                </div>
              </div>
            )}

            {isUploading && (
              <div className={styles["upload-progress"]}>
                <Progress
                  percent={Math.round(uploadProgress)}
                  status="active"
                  strokeColor="#1890ff"
                />
                <div className={styles["progress-text"]}>
                  正在上传SCORM包...
                </div>
              </div>
            )}

            {isParsing && (
              <div className={styles["upload-progress"]}>
                <Progress
                  percent={Math.round(parseProgress)}
                  status="active"
                  strokeColor="#52c41a"
                />
                <div className={styles["progress-text"]}>
                  正在解析SCORM包结构...
                </div>
              </div>
            )}
          </Form.Item>

          <Form.Item
            label="包名称"
            name="name"
            rules={[{ required: true, message: "请输入包名称" }]}
          >
            <Input
              placeholder="请输入SCORM包名称"
              disabled={isProcessing}
            />
          </Form.Item>

          <Form.Item
            label="包描述"
            name="description"
          >
            <TextArea
              placeholder="请输入SCORM包描述（可选）"
              rows={3}
              disabled={isProcessing}
            />
          </Form.Item>

          <Form.Item
            label="所属分类"
            name="category_ids"
          >
            <Select
              mode="multiple"
              placeholder="请选择所属分类（可选）"
              disabled={isProcessing}
            >
              {categories.map((item) => (
                <Option key={item.id} value={item.id}>
                  {item.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={isProcessing}
                disabled={!selectedFile}
              >
                {isUploading ? "上传中..." : isParsing ? "解析中..." : "开始上传"}
              </Button>
              <Button
                onClick={() => {
                  if (!isProcessing) {
                    setShowModal(false);
                    setSelectedFile(null);
                    form.resetFields();
                  }
                }}
                disabled={isProcessing}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
    </Modal>
  );
};

export default UploadScormSub;
