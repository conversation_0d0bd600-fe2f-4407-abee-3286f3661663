import React, { useState } from "react";
import {
  Modal,
  Form,
  Input,
  DatePicker,
  InputNumber,
  Switch,
  message,
  Upload,
  Button,
} from "antd";
import { UploadOutlined } from "@ant-design/icons";
import { live } from "../../../../api/index";
import dayjs from "dayjs";

const { TextArea } = Input;
const { RangePicker } = DatePicker;

interface Props {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

export const CreateLiveSession: React.FC<Props> = ({
  open,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const onFinish = async (values: any) => {
    if (loading) return;

    setLoading(true);
    try {
      const params = {
        title: values.title,
        description: values.description || "",
        start_time: values.time_range[0].format("YYYY-MM-DD HH:mm:ss"),
        end_time: values.time_range[1].format("YYYY-MM-DD HH:mm:ss"),
        max_viewers: values.max_viewers || 0,
        is_record: values.is_record ? 1 : 0,
        thumb: values.thumb || 0,
      };

      await live.createSession(params);
      message.success("创建成功");
      form.resetFields();
      onSuccess();
    } catch (error) {
      console.error("创建失败:", error);
    } finally {
      setLoading(false);
    }
  };

  const disabledDate = (current: any) => {
    return current && current < dayjs().startOf("day");
  };

  return (
    <Modal
      title="创建直播"
      open={open}
      onCancel={onCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          max_viewers: 100,
          is_record: true,
        }}
      >
        <Form.Item
          label="直播标题"
          name="title"
          rules={[{ required: true, message: "请输入直播标题" }]}
        >
          <Input placeholder="请输入直播标题" />
        </Form.Item>

        <Form.Item label="直播描述" name="description">
          <TextArea
            rows={4}
            placeholder="请输入直播描述"
            maxLength={500}
            showCount
          />
        </Form.Item>

        <Form.Item
          label="直播时间"
          name="time_range"
          rules={[{ required: true, message: "请选择直播时间" }]}
        >
          <RangePicker
            showTime={{ format: "HH:mm" }}
            format="YYYY-MM-DD HH:mm"
            placeholder={["开始时间", "结束时间"]}
            disabledDate={disabledDate}
            style={{ width: "100%" }}
          />
        </Form.Item>

        <Form.Item
          label="最大观看人数"
          name="max_viewers"
          rules={[{ required: true, message: "请输入最大观看人数" }]}
        >
          <InputNumber
            min={1}
            max={10000}
            placeholder="请输入最大观看人数"
            style={{ width: "100%" }}
          />
        </Form.Item>

        <Form.Item label="是否录制" name="is_record" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="直播封面" name="thumb">
          <Upload
            listType="picture-card"
            maxCount={1}
            beforeUpload={() => false}
          >
            <div>
              <UploadOutlined />
              <div style={{ marginTop: 8 }}>上传封面</div>
            </div>
          </Upload>
        </Form.Item>

        <Form.Item>
          <div style={{ textAlign: "right" }}>
            <Button onClick={onCancel} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              创建
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};
