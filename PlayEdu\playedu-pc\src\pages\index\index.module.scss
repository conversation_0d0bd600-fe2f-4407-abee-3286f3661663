.top-cont {
  width: 1200px;
  height: auto;
  margin: 0 auto;
  padding-top: 50px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  .top-item {
    width: 588px;
    height: 132px;
    background: #ffffff;
    box-shadow: 0px 4px 16px 4px rgba(0, 0, 0, 0.04);
    border-radius: 12px;
    box-sizing: border-box;
    padding: 24px;

    .title {
      width: 100%;
      height: 36px;
      display: flex;
      align-items: center;
      .icon {
        width: 36px;
        height: 36px;
        margin-right: 16px;
      }
      span {
        font-size: 20px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.88);
        line-height: 36px;
      }
    }
    .info {
      margin-top: 24px;
      width: 100%;
      height: 24px;
      display: flex;
      align-items: center;
      .info-item {
        width: auto;
        font-size: 16px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.45);
        line-height: 24px;
        margin-right: 50px;
        &:last-child {
          margin-right: 0px;
        }
        strong {
          font-size: 20px;
          color: rgba(0, 0, 0, 0.88);
        }
      }
    }
  }
}
.tabs {
  width: 1200px;
  height: 48px;
  margin: 0 auto;
  margin-top: 50px;
  display: flex;
  align-items: center;
  position: relative;
  .tab-item {
    width: 64px;
    height: 48px;
    margin-right: 50px;
    transition: all 0.2s;
    position: relative;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
      .tit {
        color: #ff4d4f;
      }
    }
    .tit {
      width: 64px;
      height: 40px;
      font-size: 16px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.88);
      line-height: 40px;
    }
  }
  .tab-active-item {
    width: 64px;
    height: 48px;
    cursor: pointer;
    margin-right: 50px;
    transition: all 0.2s;

    &:hover {
      opacity: 0.8;
    }
    .tit {
      width: 64px;
      height: 40px;
      font-size: 16px;
      font-weight: 500;
      color: #ff4d4f;
      line-height: 40px;
    }
    .banner {
      animation: scaleTransX 0.3s;
    }
  }
  .dropButton {
    height: 40px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    line-height: 40px;
    display: flex;
    align-items: center;
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer;
  }
}

@keyframes scaleTransX {
  0% {
    transform: translateX(0px);
  }

  50% {
    transform: translateX(6px);
  }

  100% {
    transform: translateX(0px);
  }
}

.courses-list {
  width: 1200px;
  height: auto;
  margin: 0 auto;
  box-sizing: border-box;
  padding-top: 34px;
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  position: relative;
}

.extra {
  width: 1200px;
  margin: 0 auto;
  margin-top: 80px;
  text-align: center;
  height: 40px;
  font-size: 16px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.2);
  line-height: 40px;
}
