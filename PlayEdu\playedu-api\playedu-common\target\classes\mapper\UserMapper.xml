<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.common.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.common.domain.User">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
            <result property="password" column="password" jdbcType="VARCHAR"/>
            <result property="salt" column="salt" jdbcType="VARCHAR"/>
            <result property="idCard" column="id_card" jdbcType="VARCHAR"/>
            <result property="credit1" column="credit1" jdbcType="INTEGER"/>
            <result property="createIp" column="create_ip" jdbcType="VARCHAR"/>
            <result property="createCity" column="create_city" jdbcType="VARCHAR"/>
            <result property="isActive" column="is_active" jdbcType="TINYINT"/>
            <result property="isLock" column="is_lock" jdbcType="TINYINT"/>
            <result property="isVerify" column="is_verify" jdbcType="TINYINT"/>
            <result property="verifyAt" column="verify_at" jdbcType="TIMESTAMP"/>
            <result property="isSetPassword" column="is_set_password" jdbcType="TINYINT"/>
            <result property="loginAt" column="login_at" jdbcType="TIMESTAMP"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,email,name,
        avatar,password,salt,
        id_card,credit1,create_ip,
        create_city,is_active,is_lock,
        is_verify,verify_at,is_set_password,
        login_at,created_at,updated_at
    </sql>
    <select id="paginateCount" resultType="java.lang.Long">
        SELECT count(1)
        FROM `users`
        <where>
            <if test="depIds != null">
                <choose>
                    <when test="depIds.size == 0">
                        AND `users`.`id` IN (
                        SELECT `users`.`id` from `users` LEFT JOIN `user_department` ON `user_department`.`user_id` =
                        `users`.`id`
                        WHERE `user_department`.`user_id` IS NULL
                        )
                    </when>
                    <otherwise>
                        AND `users`.`id` IN (
                        SELECT `users`.`id` from `users` LEFT JOIN `user_department` ON `user_department`.`user_id` =
                        `users`.`id`
                        WHERE `user_department`.`dep_id` IN (<foreach collection="depIds" item="depId" separator=",">
                        #{depId}</foreach>)
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="name != null and name != ''">
                AND `users`.`name` LIKE concat('%',#{name},'%')
            </if>
            <if test="email != null and email != ''">
                AND `users`.`email` LIKE concat('%',#{email},'%')
            </if>
            <if test="idCard != null and idCard != ''">
                AND `users`.`id_card` = #{idCard}
            </if>
            <if test="isActive != null">
                AND `users`.`is_active` = #{isActive}
            </if>
            <if test="isLock != null">
                AND `users`.`is_lock` = #{isLock}
            </if>
            <if test="isVerify != null">
                AND `users`.`is_verify` = #{isVerify}
            </if>
            <if test="isSetPassword != null">
                AND `users`.`is_set_password` = #{isSetPassword}
            </if>
            <if test="createdAt != null and createdAt.size == 2">
                AND `users`.`created_at` BETWEEN
                <foreach collection="createdAt" item="createdAtItem" separator=" AND ">#{createdAtItem}</foreach>
            </if>
        </where>
    </select>

    <select id="paginate" resultType="xyz.playedu.common.domain.User">
        SELECT `users`.*
        FROM `users`
        <where>
            <if test="depIds != null">
                <choose>
                    <when test="depIds.size == 0">
                        AND `users`.`id` IN (
                        SELECT `users`.`id` from `users` LEFT JOIN `user_department` ON `user_department`.`user_id` =
                        `users`.`id`
                        WHERE `user_department`.`user_id` IS NULL
                        )
                    </when>
                    <otherwise>
                        AND `users`.`id` IN (
                        SELECT `users`.`id` from `users` LEFT JOIN `user_department` ON `user_department`.`user_id` =
                        `users`.`id`
                        WHERE `user_department`.`dep_id` IN (<foreach collection="depIds" item="depId" separator=",">
                        #{depId}</foreach>)
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="name != null and name != ''">
                AND `users`.`name` LIKE concat('%',#{name},'%')
            </if>
            <if test="email != null and email != ''">
                AND `users`.`email` LIKE concat('%',#{email},'%')
            </if>
            <if test="idCard != null and idCard != ''">
                AND `users`.`id_card` = #{idCard}
            </if>
            <if test="isActive != null">
                AND `users`.`is_active` = #{isActive}
            </if>
            <if test="isLock != null">
                AND `users`.`is_lock` = #{isLock}
            </if>
            <if test="isVerify != null">
                AND `users`.`is_verify` = #{isVerify}
            </if>
            <if test="isSetPassword != null">
                AND `users`.`is_set_password` = #{isSetPassword}
            </if>
            <if test="createdAt != null and createdAt.size == 2">
                AND `users`.`created_at` BETWEEN
                <foreach collection="createdAt" item="createdAtItem" separator=" AND ">#{createdAtItem}</foreach>
            </if>
        </where>
        <if test="sortAlgo == 'asc'">
            <choose>
                <when test="sortField == 'size'">
                    ORDER BY `users`.`size` ASC
                </when>
                <when test="sortField == 'created_at'">
                    ORDER BY `users`.`created_at` ASC
                </when>
                <when test="sortField == 'credit1'">
                    ORDER BY `users`.`credit1` ASC
                </when>
                <otherwise>
                    ORDER BY `users`.`id` ASC
                </otherwise>
            </choose>
        </if>
        <if test="sortAlgo != 'asc'">
            <choose>
                <when test="sortField == 'size'">
                    ORDER BY `users`.`size` DESC
                </when>
                <when test="sortField == 'created_at'">
                    ORDER BY `users`.`created_at` DESC
                </when>
                <when test="sortField == 'credit1'">
                    ORDER BY `users`.`credit1` DESC
                </when>
                <otherwise>
                    ORDER BY `users`.`id` DESC
                </otherwise>
            </choose>
        </if>
        LIMIT #{pageStart}, #{pageSize};
    </select>
</mapper>
