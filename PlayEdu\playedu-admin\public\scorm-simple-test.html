<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SCORM API 简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { background: #f6ffed; border-left: 4px solid #52c41a; }
        .error { background: #fff2f0; border-left: 4px solid #ff4d4f; }
    </style>
</head>
<body>
    <h1>SCORM API 简单测试</h1>
    
    <div>
        <h3>步骤1：先登录管理后台</h3>
        <p>请先在新标签页中打开 <a href="http://localhost:3001" target="_blank">http://localhost:3001</a> 并登录</p>
        <p>登录后回到这个页面进行测试</p>
    </div>

    <div>
        <h3>步骤2：测试SCORM API</h3>
        <button onclick="testInitialize()">测试初始化</button>
        <button onclick="testWithAuth()">使用认证测试</button>
        <button onclick="testDirectAPI()">直接API测试</button>
    </div>

    <div id="result" class="result"></div>

    <script>
        function log(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : '');
            resultDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
        }

        function clearLog() {
            document.getElementById('result').innerHTML = '';
        }

        // 测试初始化
        async function testInitialize() {
            clearLog();
            log('开始测试SCORM API初始化...');

            try {
                const response = await fetch('http://localhost:9898/api/v1/scorm/packages/4/initialize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({})
                });

                const result = await response.json();
                log(`响应状态: ${response.status}`);
                log(`响应内容: ${JSON.stringify(result, null, 2)}`);

                if (result.code === 200) {
                    log('✅ 初始化成功！', 'success');
                } else {
                    log(`❌ 初始化失败: ${result.msg}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求异常: ${error.message}`, 'error');
            }
        }

        // 使用认证测试
        async function testWithAuth() {
            clearLog();
            log('使用认证信息测试...');

            // 尝试获取token
            const token = localStorage.getItem('playedu-pc-token') || 
                         localStorage.getItem('token') ||
                         sessionStorage.getItem('playedu-pc-token');

            log(`Token: ${token ? '已找到' : '未找到'}`);

            try {
                const headers = {
                    'Content-Type': 'application/json',
                };

                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch('http://localhost:9898/api/v1/scorm/packages/4/initialize', {
                    method: 'POST',
                    headers: headers,
                    credentials: 'include',
                    body: JSON.stringify({})
                });

                const result = await response.json();
                log(`响应状态: ${response.status}`);
                log(`响应内容: ${JSON.stringify(result, null, 2)}`);

                if (result.code === 200) {
                    log('✅ 认证测试成功！', 'success');
                } else {
                    log(`❌ 认证测试失败: ${result.msg}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求异常: ${error.message}`, 'error');
            }
        }

        // 直接API测试
        async function testDirectAPI() {
            clearLog();
            log('测试API可达性...');

            try {
                // 先测试一个简单的GET请求
                const response = await fetch('http://localhost:9898/backend/v1/scorm/packages/4', {
                    method: 'GET',
                    credentials: 'include'
                });

                log(`GET请求状态: ${response.status}`);
                
                if (response.ok) {
                    const result = await response.json();
                    log(`GET请求成功: ${JSON.stringify(result, null, 2)}`);
                    log('✅ 后端API可达', 'success');
                } else {
                    log(`❌ GET请求失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ API不可达: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示说明
        window.onload = function() {
            log('SCORM API测试页面已加载');
            log('请按照步骤进行测试：');
            log('1. 先登录管理后台');
            log('2. 回到这个页面测试API');
        };
    </script>
</body>
</html>
