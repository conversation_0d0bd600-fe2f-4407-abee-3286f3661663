.title {
  width: 100%;
  box-sizing: border-box;
  padding: 20px;
  text-align: left;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.88);
  line-height: 24px;
}
.list-box {
  width: 100%;
  float: left;
  height: auto;
  box-sizing: border-box;
  padding: 0px 20px 60px 20px;
  text-align: left;
  overflow-x: hidden;
  overflow-y: auto;
  padding-bottom: calc(
    80px + constant(safe-area-inset-bottom)
  ); /* 兼容iOS 11.0 - 11.2 */
  padding-bottom: calc(60px + env(safe-area-inset-bottom)); /* 兼容iOS 11.2+ */
  .label {
    width: 100%;
    height: 40px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    line-height: 40px;
  }
  .item {
    width: 100%;
    height: 75px;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .item-info {
      flex: 1;
      height: 75px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .item-title {
        width: 100%;
        height: 42px;
        font-size: 15px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.88);
        line-height: 21px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .item-record {
        width: 100%;
        display: flex;
        align-items: center;
        .type {
          width: 46px;
          height: 24px;
          background: rgba(255, 77, 79, 0.1);
          border-radius: 4px;
          font-size: 12px;
          font-weight: 400;
          color: #ff4d4f;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
        }
        .active-type {
          width: 46px;
          height: 24px;
          background: rgba(#ff9900, 0.1);
          border-radius: 4px;
          font-size: 12px;
          font-weight: 400;
          color: #ff9900;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
        }
        .tip {
          flex: 1;
          height: 12px;
          font-size: 12px;
          font-weight: 400;
          color: #ff4d4f;
          line-height: 12px;
          margin-left: 5px;
        }
      }
    }
  }
}
