.upload-scorm-modal {
  .upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    background: #fafafa;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: #1890ff;
      background: #f0f8ff;
    }
    
    &.dragover {
      border-color: #1890ff;
      background: #f0f8ff;
    }
    
    .upload-icon {
      font-size: 48px;
      color: #1890ff;
      margin-bottom: 16px;
    }
    
    .upload-text {
      font-size: 16px;
      color: #666;
      margin-bottom: 8px;
    }
    
    .upload-hint {
      font-size: 12px;
      color: #999;
    }
  }
  
  .file-info {
    margin-top: 16px;
    padding: 12px;
    background: #f0f8ff;
    border: 1px solid #d4edda;
    border-radius: 6px;
    
    .file-name {
      font-weight: 500;
      color: #155724;
      margin-bottom: 4px;
    }
    
    .file-size {
      font-size: 12px;
      color: #666;
    }
  }
  
  .progress-section {
    margin-top: 16px;
    
    .progress-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }
    
    .ant-progress {
      margin-bottom: 8px;
    }
  }
  
  .form-section {
    margin-top: 24px;
    
    .ant-form-item {
      margin-bottom: 16px;
    }
    
    .ant-form-item-label > label {
      font-weight: 500;
    }
  }
  
  .tips-section {
    margin-top: 16px;
    padding: 12px;
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 6px;
    
    .tips-title {
      font-weight: 500;
      margin-bottom: 8px;
      color: #d46b08;
    }
    
    .tips-list {
      margin: 0;
      padding-left: 20px;
      color: #d46b08;
      
      li {
        margin-bottom: 4px;
        font-size: 12px;
      }
    }
  }
  
  .modal-footer {
    text-align: right;
    margin-top: 24px;
    
    .ant-btn {
      margin-left: 8px;
    }
  }
}

.upload-scorm-button {
  .ant-btn {
    display: inline-flex;
    align-items: center;
    
    .anticon {
      margin-right: 6px;
    }
  }
}

.scorm-upload-status {
  .status-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .status-icon {
      margin-right: 8px;
      
      &.success {
        color: #52c41a;
      }
      
      &.processing {
        color: #1890ff;
      }
      
      &.error {
        color: #ff4d4f;
      }
    }
    
    .status-text {
      flex: 1;
      font-size: 14px;
    }
  }
}

.file-validation {
  .validation-item {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;
    
    .validation-icon {
      margin-right: 6px;
      
      &.success {
        color: #52c41a;
      }
      
      &.error {
        color: #ff4d4f;
      }
    }
  }
}

@media (max-width: 768px) {
  .upload-scorm-modal {
    .upload-area {
      padding: 30px 15px;
      
      .upload-icon {
        font-size: 36px;
      }
      
      .upload-text {
        font-size: 14px;
      }
    }
    
    .form-section {
      .ant-form-item-label {
        text-align: left;
      }
    }
  }
}
