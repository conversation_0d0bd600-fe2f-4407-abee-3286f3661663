/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import xyz.playedu.course.domain.ScormPackage;

/**
 * SCORM包服务接口
 *
 * <AUTHOR> Team
 */
public interface ScormPackageService extends IService<ScormPackage> {

    /**
     * 根据资源ID获取SCORM包
     *
     * @param resourceId 资源ID
     * @return SCORM包信息
     */
    ScormPackage getByResourceId(Integer resourceId);

    /**
     * 创建SCORM包
     *
     * @param scormPackage SCORM包信息
     * @return 创建的SCORM包
     */
    ScormPackage createPackage(ScormPackage scormPackage);

    /**
     * 更新SCORM包
     *
     * @param id SCORM包ID
     * @param scormPackage SCORM包信息
     * @return 更新的SCORM包
     */
    ScormPackage updatePackage(Integer id, ScormPackage scormPackage);

    /**
     * 删除SCORM包
     *
     * @param id SCORM包ID
     */
    void deletePackage(Integer id);

    /**
     * 重新解析SCORM包
     *
     * @param id SCORM包ID
     * @return 是否成功
     */
    boolean reparsePackage(Integer id);

    /**
     * 更新解析状态
     *
     * @param id SCORM包ID
     * @param status 解析状态
     * @param error 错误信息
     */
    void updateParseStatus(Integer id, Integer status, String error);
}
