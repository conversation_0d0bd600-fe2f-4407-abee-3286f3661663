/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.course.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 直播场次表
 * @TableName live_sessions
 */
@TableName(value = "live_sessions")
@Data
public class LiveSession implements Serializable {
    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 直播标题 */
    private String title;

    /** 直播描述 */
    private String description;

    /** 直播封面 */
    private Integer thumb;

    /** 直播推流地址 */
    @JsonProperty("push_url")
    private String pushUrl;

    /** 直播拉流地址 */
    @JsonProperty("pull_url")
    private String pullUrl;

    /** HLS播放地址 */
    @JsonProperty("hls_url")
    private String hlsUrl;

    /** 直播状态：0-未开始，1-直播中，2-已结束 */
    private Integer status;

    /** 开始时间 */
    @JsonProperty("start_time")
    private Date startTime;

    /** 结束时间 */
    @JsonProperty("end_time")
    private Date endTime;

    /** 实际开始时间 */
    @JsonProperty("actual_start_time")
    private Date actualStartTime;

    /** 实际结束时间 */
    @JsonProperty("actual_end_time")
    private Date actualEndTime;

    /** 最大观看人数 */
    @JsonProperty("max_viewers")
    private Integer maxViewers;

    /** 当前观看人数 */
    @JsonProperty("current_viewers")
    private Integer currentViewers;

    /** 是否录制 */
    @JsonProperty("is_record")
    private Integer isRecord;

    /** 录制文件地址 */
    @JsonProperty("record_url")
    private String recordUrl;

    /** 是否回放 */
    @JsonProperty("is_playback")
    private Integer isPlayback;

    /** 回放地址 */
    @JsonProperty("playback_url")
    private String playbackUrl;

    /** 创建人ID */
    @JsonProperty("admin_id")
    private Integer adminId;

    /** 创建时间 */
    @JsonProperty("created_at")
    private Date createdAt;

    /** 更新时间 */
    @JsonProperty("updated_at")
    private Date updatedAt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        LiveSession other = (LiveSession) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getTitle() == null ? other.getTitle() == null : this.getTitle().equals(other.getTitle()))
                && (this.getDescription() == null ? other.getDescription() == null : this.getDescription().equals(other.getDescription()))
                && (this.getThumb() == null ? other.getThumb() == null : this.getThumb().equals(other.getThumb()))
                && (this.getPushUrl() == null ? other.getPushUrl() == null : this.getPushUrl().equals(other.getPushUrl()))
                && (this.getPullUrl() == null ? other.getPullUrl() == null : this.getPullUrl().equals(other.getPullUrl()))
                && (this.getHlsUrl() == null ? other.getHlsUrl() == null : this.getHlsUrl().equals(other.getHlsUrl()))
                && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
                && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
                && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
                && (this.getActualStartTime() == null ? other.getActualStartTime() == null : this.getActualStartTime().equals(other.getActualStartTime()))
                && (this.getActualEndTime() == null ? other.getActualEndTime() == null : this.getActualEndTime().equals(other.getActualEndTime()))
                && (this.getMaxViewers() == null ? other.getMaxViewers() == null : this.getMaxViewers().equals(other.getMaxViewers()))
                && (this.getCurrentViewers() == null ? other.getCurrentViewers() == null : this.getCurrentViewers().equals(other.getCurrentViewers()))
                && (this.getIsRecord() == null ? other.getIsRecord() == null : this.getIsRecord().equals(other.getIsRecord()))
                && (this.getRecordUrl() == null ? other.getRecordUrl() == null : this.getRecordUrl().equals(other.getRecordUrl()))
                && (this.getIsPlayback() == null ? other.getIsPlayback() == null : this.getIsPlayback().equals(other.getIsPlayback()))
                && (this.getPlaybackUrl() == null ? other.getPlaybackUrl() == null : this.getPlaybackUrl().equals(other.getPlaybackUrl()))
                && (this.getAdminId() == null ? other.getAdminId() == null : this.getAdminId().equals(other.getAdminId()))
                && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
                && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTitle() == null) ? 0 : getTitle().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getThumb() == null) ? 0 : getThumb().hashCode());
        result = prime * result + ((getPushUrl() == null) ? 0 : getPushUrl().hashCode());
        result = prime * result + ((getPullUrl() == null) ? 0 : getPullUrl().hashCode());
        result = prime * result + ((getHlsUrl() == null) ? 0 : getHlsUrl().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getActualStartTime() == null) ? 0 : getActualStartTime().hashCode());
        result = prime * result + ((getActualEndTime() == null) ? 0 : getActualEndTime().hashCode());
        result = prime * result + ((getMaxViewers() == null) ? 0 : getMaxViewers().hashCode());
        result = prime * result + ((getCurrentViewers() == null) ? 0 : getCurrentViewers().hashCode());
        result = prime * result + ((getIsRecord() == null) ? 0 : getIsRecord().hashCode());
        result = prime * result + ((getRecordUrl() == null) ? 0 : getRecordUrl().hashCode());
        result = prime * result + ((getIsPlayback() == null) ? 0 : getIsPlayback().hashCode());
        result = prime * result + ((getPlaybackUrl() == null) ? 0 : getPlaybackUrl().hashCode());
        result = prime * result + ((getAdminId() == null) ? 0 : getAdminId().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", title=").append(title);
        sb.append(", description=").append(description);
        sb.append(", thumb=").append(thumb);
        sb.append(", pushUrl=").append(pushUrl);
        sb.append(", pullUrl=").append(pullUrl);
        sb.append(", hlsUrl=").append(hlsUrl);
        sb.append(", status=").append(status);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", actualStartTime=").append(actualStartTime);
        sb.append(", actualEndTime=").append(actualEndTime);
        sb.append(", maxViewers=").append(maxViewers);
        sb.append(", currentViewers=").append(currentViewers);
        sb.append(", isRecord=").append(isRecord);
        sb.append(", recordUrl=").append(recordUrl);
        sb.append(", isPlayback=").append(isPlayback);
        sb.append(", playbackUrl=").append(playbackUrl);
        sb.append(", adminId=").append(adminId);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
