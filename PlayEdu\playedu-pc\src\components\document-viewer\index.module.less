.document-viewer {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;

  .document-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;

    h4 {
      margin: 0;
      color: #262626;
    }
  }

  .document-toolbar {
    padding: 12px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .scale-text {
      font-size: 12px;
      color: #666;
      margin: 0 8px;
      min-width: 40px;
      text-align: center;
    }
  }

  .document-container {
    position: relative;
    background: #f5f5f5;

    .loading-container {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
      z-index: 10;
    }

    iframe {
      display: block;
      transition: transform 0.3s ease;
    }

    .text-viewer {
      overflow: auto;
      background: #fff;
      
      iframe {
        transform-origin: top left;
      }
    }
  }

  // 全屏样式
  &:fullscreen {
    .document-container {
      height: 100vh !important;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .document-viewer {
    .document-header {
      padding: 12px 16px;

      h4 {
        font-size: 16px;
      }
    }

    .document-toolbar {
      padding: 8px 16px;
      flex-wrap: wrap;
      gap: 8px;

      .scale-text {
        margin: 0 4px;
      }
    }
  }
}

@media (max-width: 480px) {
  .document-viewer {
    .document-header {
      padding: 8px 12px;

      h4 {
        font-size: 14px;
      }
    }

    .document-toolbar {
      padding: 6px 12px;

      :global(.ant-btn) {
        font-size: 12px;
        height: 28px;
        padding: 0 8px;
      }
    }
  }
}

// PDF.js 样式覆盖
:global(.pdfViewer) {
  .page {
    margin: 10px auto;
    border: 1px solid #ccc;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// Office Online 样式
:global(.office-frame) {
  border: none;
  width: 100%;
  height: 100%;
}
