<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SCORM API 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .info { color: #1890ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>SCORM API 测试页面</h1>
        <p>这个页面用于测试SCORM API的各种功能。请确保后端服务正在运行。</p>

        <!-- 配置区域 -->
        <div class="section">
            <h3>配置</h3>
            <label>SCORM包ID: <input type="number" id="packageId" value="4" /></label>
            <label>用户ID: <input type="number" id="userId" value="1" /></label>
            <button onclick="updateConfig()">更新配置</button>
        </div>

        <!-- 基本API测试 -->
        <div class="section">
            <h3>基本API测试</h3>
            <button onclick="testInitialize()">初始化 (Initialize)</button>
            <button onclick="testGetValue()">获取值 (GetValue)</button>
            <button onclick="testSetValue()">设置值 (SetValue)</button>
            <button onclick="testCommit()">提交 (Commit)</button>
            <button onclick="testTerminate()">终止 (Terminate)</button>
        </div>

        <!-- 学习数据测试 -->
        <div class="section">
            <h3>学习数据测试</h3>
            <div>
                <label>数据元素: 
                    <select id="dataElement">
                        <option value="cmi.core.lesson_status">课程状态</option>
                        <option value="cmi.core.score.raw">原始分数</option>
                        <option value="cmi.core.session_time">会话时间</option>
                        <option value="cmi.location">位置</option>
                        <option value="cmi.suspend_data">暂停数据</option>
                    </select>
                </label>
                <label>值: <input type="text" id="dataValue" placeholder="输入值" /></label>
                <button onclick="setCustomValue()">设置自定义值</button>
            </div>
            <div style="margin-top: 10px;">
                <button onclick="simulateLearning()">模拟学习过程</button>
                <button onclick="simulateCompletion()">模拟完成学习</button>
            </div>
        </div>

        <!-- 日志区域 -->
        <div class="section">
            <h3>操作日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        // 配置
        let config = {
            packageId: 4,
            userId: 1,
            apiBaseUrl: 'http://localhost:9898/api/v1/scorm'
        };

        let attemptId = null;

        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 更新配置
        function updateConfig() {
            config.packageId = parseInt(document.getElementById('packageId').value);
            config.userId = parseInt(document.getElementById('userId').value);
            log(`配置已更新: 包ID=${config.packageId}, 用户ID=${config.userId}`, 'info');
        }

        // HTTP请求函数
        async function apiRequest(method, url, data = null) {
            try {
                // 尝试从localStorage获取token
                const token = localStorage.getItem('token') || localStorage.getItem('playedu-pc-token');

                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                };

                // 如果有token，添加到请求头
                if (token) {
                    options.headers['Authorization'] = `Bearer ${token}`;
                }

                if (data) {
                    if (method === 'GET') {
                        const params = new URLSearchParams(data);
                        url += '?' + params.toString();
                    } else {
                        options.body = JSON.stringify(data);
                    }
                }

                log(`发送请求: ${method} ${url}`, 'info');
                const response = await fetch(url, options);
                const result = await response.json();
                
                if (result.code === 200) {
                    log(`请求成功: ${JSON.stringify(result)}`, 'success');
                } else {
                    log(`请求失败: ${result.msg}`, 'error');
                }
                
                return result;
            } catch (error) {
                log(`请求异常: ${error.message}`, 'error');
                throw error;
            }
        }

        // 测试初始化
        async function testInitialize() {
            try {
                const result = await apiRequest('POST', 
                    `${config.apiBaseUrl}/packages/${config.packageId}/initialize`,
                    { userId: config.userId }
                );
                
                if (result.code === 200) {
                    attemptId = result.data.attempt_id;
                    log(`初始化成功，尝试ID: ${attemptId}`, 'success');
                }
            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error');
            }
        }

        // 测试获取值
        async function testGetValue() {
            if (!attemptId) {
                log('请先初始化', 'error');
                return;
            }

            try {
                const element = 'cmi.core.lesson_status';
                const result = await apiRequest('GET', 
                    `${config.apiBaseUrl}/packages/${config.packageId}/getValue`,
                    { element: element, userId: config.userId }
                );
                
                if (result.code === 200) {
                    log(`获取值成功: ${element} = ${result.data.value}`, 'success');
                }
            } catch (error) {
                log(`获取值失败: ${error.message}`, 'error');
            }
        }

        // 测试设置值
        async function testSetValue() {
            if (!attemptId) {
                log('请先初始化', 'error');
                return;
            }

            try {
                const result = await apiRequest('POST', 
                    `${config.apiBaseUrl}/attempts/${attemptId}/data`,
                    { 
                        element: 'cmi.core.lesson_status', 
                        value: 'incomplete' 
                    }
                );
                
                if (result.code === 200) {
                    log('设置值成功: cmi.core.lesson_status = incomplete', 'success');
                }
            } catch (error) {
                log(`设置值失败: ${error.message}`, 'error');
            }
        }

        // 测试提交
        async function testCommit() {
            if (!attemptId) {
                log('请先初始化', 'error');
                return;
            }

            try {
                const result = await apiRequest('POST', 
                    `${config.apiBaseUrl}/attempts/${attemptId}/commit`
                );
                
                if (result.code === 200) {
                    log('提交成功', 'success');
                }
            } catch (error) {
                log(`提交失败: ${error.message}`, 'error');
            }
        }

        // 测试终止
        async function testTerminate() {
            if (!attemptId) {
                log('请先初始化', 'error');
                return;
            }

            try {
                const result = await apiRequest('POST', 
                    `${config.apiBaseUrl}/attempts/${attemptId}/terminate`
                );
                
                if (result.code === 200) {
                    log('终止成功', 'success');
                    attemptId = null;
                }
            } catch (error) {
                log(`终止失败: ${error.message}`, 'error');
            }
        }

        // 设置自定义值
        async function setCustomValue() {
            if (!attemptId) {
                log('请先初始化', 'error');
                return;
            }

            const element = document.getElementById('dataElement').value;
            const value = document.getElementById('dataValue').value;

            if (!value) {
                log('请输入值', 'error');
                return;
            }

            try {
                const result = await apiRequest('POST', 
                    `${config.apiBaseUrl}/attempts/${attemptId}/data`,
                    { element: element, value: value }
                );
                
                if (result.code === 200) {
                    log(`设置成功: ${element} = ${value}`, 'success');
                }
            } catch (error) {
                log(`设置失败: ${error.message}`, 'error');
            }
        }

        // 模拟学习过程
        async function simulateLearning() {
            if (!attemptId) {
                await testInitialize();
                if (!attemptId) return;
            }

            log('开始模拟学习过程...', 'info');

            // 设置学习状态为进行中
            await apiRequest('POST', `${config.apiBaseUrl}/attempts/${attemptId}/data`,
                { element: 'cmi.core.lesson_status', value: 'incomplete' });

            // 设置位置
            await apiRequest('POST', `${config.apiBaseUrl}/attempts/${attemptId}/data`,
                { element: 'cmi.location', value: 'page_3' });

            // 设置会话时间
            await apiRequest('POST', `${config.apiBaseUrl}/attempts/${attemptId}/data`,
                { element: 'cmi.core.session_time', value: 'PT0H15M30S' });

            // 提交数据
            await testCommit();

            log('学习过程模拟完成', 'success');
        }

        // 模拟完成学习
        async function simulateCompletion() {
            if (!attemptId) {
                log('请先开始学习', 'error');
                return;
            }

            log('模拟完成学习...', 'info');

            // 设置完成状态
            await apiRequest('POST', `${config.apiBaseUrl}/attempts/${attemptId}/data`,
                { element: 'cmi.core.lesson_status', value: 'completed' });

            // 设置分数
            await apiRequest('POST', `${config.apiBaseUrl}/attempts/${attemptId}/data`,
                { element: 'cmi.core.score.raw', value: '95' });

            // 最终提交
            await testCommit();

            // 终止会话
            await testTerminate();

            log('学习完成模拟结束', 'success');
        }

        // 页面加载时的初始化
        window.onload = function() {
            log('SCORM API测试页面已加载', 'info');
            log('请先点击"初始化"按钮开始测试', 'info');
        };
    </script>
</body>
</html>
