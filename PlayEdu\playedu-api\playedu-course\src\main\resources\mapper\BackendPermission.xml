<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.course.mapper.UserLearnDurationRecordMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.course.domain.UserLearnDurationRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="INTEGER"/>
            <result property="createdDate" column="created_date" jdbcType="DATE"/>
            <result property="duration" column="duration" jdbcType="INTEGER"/>
            <result property="startAt" column="start_at" jdbcType="TIMESTAMP"/>
            <result property="endAt" column="end_at" jdbcType="TIMESTAMP"/>
            <result property="courseId" column="course_id" jdbcType="INTEGER"/>
            <result property="hourId" column="hour_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,created_date,
        duration,start_at,end_at,
        course_id,hour_id
    </sql>
</mapper>
