import React, { useEffect, useState } from "react";
import { Spin, Alert, Button, Space, Typography } from "antd";
import {
  DownloadOutlined,
  FullscreenOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
} from "@ant-design/icons";
import styles from "./index.module.less";

const { Title } = Typography;

interface DocumentViewerProps {
  src: string;
  title?: string;
  extension: string;
  onDownload?: () => void;
  showToolbar?: boolean;
  height?: number | string;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({
  src,
  title,
  extension,
  onDownload,
  showToolbar = true,
  height = "600px",
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [scale, setScale] = useState(1);

  useEffect(() => {
    setLoading(true);
    setError(null);
  }, [src]);

  const handleLoad = () => {
    setLoading(false);
  };

  const handleError = () => {
    setLoading(false);
    setError("文档加载失败，请检查文件是否存在或格式是否正确");
  };

  const handleZoomIn = () => {
    setScale(prev => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setScale(prev => Math.max(prev - 0.25, 0.5));
  };

  const handleFullscreen = () => {
    const element = document.querySelector(`.${styles["document-container"]}`);
    if (element) {
      if (element.requestFullscreen) {
        element.requestFullscreen();
      }
    }
  };

  const renderPdfViewer = () => {
    return (
      <iframe
        src={`/pdf-viewer?file=${encodeURIComponent(src)}`}
        width="100%"
        height={height}
        style={{ border: "none" }}
        onLoad={handleLoad}
        onError={handleError}
        title={title || "PDF文档"}
      />
    );
  };

  const renderOfficeViewer = () => {
    const officeOnlineUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(src)}`;
    
    return (
      <iframe
        src={officeOnlineUrl}
        width="100%"
        height={height}
        style={{ border: "none" }}
        onLoad={handleLoad}
        onError={handleError}
        title={title || "Office文档"}
      />
    );
  };

  const renderTextViewer = () => {
    return (
      <div className={styles["text-viewer"]} style={{ height }}>
        <iframe
          src={src}
          width="100%"
          height="100%"
          style={{ border: "none", transform: `scale(${scale})` }}
          onLoad={handleLoad}
          onError={handleError}
          title={title || "文本文档"}
        />
      </div>
    );
  };

  const renderH5Viewer = () => {
    return (
      <iframe
        src={src}
        width="100%"
        height={height}
        style={{ border: "none", transform: `scale(${scale})` }}
        onLoad={handleLoad}
        onError={handleError}
        title={title || "H5课件"}
        sandbox="allow-scripts allow-same-origin allow-forms"
      />
    );
  };

  const renderViewer = () => {
    const ext = extension.toLowerCase();
    
    switch (ext) {
      case "pdf":
        return renderPdfViewer();
      case "doc":
      case "docx":
      case "ppt":
      case "pptx":
      case "xls":
      case "xlsx":
        return renderOfficeViewer();
      case "txt":
        return renderTextViewer();
      case "html":
      case "htm":
        return renderH5Viewer();
      default:
        return (
          <Alert
            message="不支持的文档格式"
            description={`当前不支持预览 .${ext} 格式的文档`}
            type="warning"
            showIcon
          />
        );
    }
  };

  return (
    <div className={styles["document-viewer"]}>
      {title && (
        <div className={styles["document-header"]}>
          <Title level={4}>{title}</Title>
        </div>
      )}

      {showToolbar && (
        <div className={styles["document-toolbar"]}>
          <Space>
            {(extension === "txt" || extension === "html" || extension === "htm") && (
              <>
                <Button
                  icon={<ZoomOutOutlined />}
                  onClick={handleZoomOut}
                  disabled={scale <= 0.5}
                  size="small"
                >
                  缩小
                </Button>
                <span className={styles["scale-text"]}>
                  {Math.round(scale * 100)}%
                </span>
                <Button
                  icon={<ZoomInOutlined />}
                  onClick={handleZoomIn}
                  disabled={scale >= 3}
                  size="small"
                >
                  放大
                </Button>
              </>
            )}
            
            <Button
              icon={<FullscreenOutlined />}
              onClick={handleFullscreen}
              size="small"
            >
              全屏
            </Button>
            
            {onDownload && (
              <Button
                icon={<DownloadOutlined />}
                onClick={onDownload}
                size="small"
              >
                下载
              </Button>
            )}
          </Space>
        </div>
      )}

      <div className={styles["document-container"]}>
        {loading && (
          <div className={styles["loading-container"]}>
            <Spin size="large" tip="文档加载中..." />
          </div>
        )}

        {error && (
          <Alert
            message="加载失败"
            description={error}
            type="error"
            showIcon
            action={
              <Button size="small" onClick={() => window.location.reload()}>
                重新加载
              </Button>
            }
          />
        )}

        {!error && renderViewer()}
      </div>
    </div>
  );
};

export default DocumentViewer;
