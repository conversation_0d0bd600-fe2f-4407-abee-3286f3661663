/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@Slf4j
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired private AdminInterceptor adminInterceptor;

    @Autowired private FrontInterceptor frontInterceptor;

    @Autowired private ApiInterceptor apiInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(apiInterceptor).addPathPatterns("/**");
        registry.addInterceptor(adminInterceptor).addPathPatterns("/backend/**");
        registry.addInterceptor(frontInterceptor)
                .addPathPatterns("/api/v1/**")
                .excludePathPatterns("/api/v1/scorm/**"); // 排除SCORM API，不需要认证
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*") // 使用allowedOriginPatterns代替allowedOrigins
                .allowedMethods("*")
                .allowedHeaders("*")
                .allowCredentials(true) // 允许携带认证信息
                .maxAge(1_296_000);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置SCORM包静态资源访问
        String workDir = System.getProperty("user.dir");
        registry.addResourceHandler("/storage/scorm/**")
                .addResourceLocations("file:" + workDir + "/storage/scorm/");

        // 配置其他静态资源
        registry.addResourceHandler("/storage/**")
                .addResourceLocations("file:" + workDir + "/storage/");
    }
}
