.live-player {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;

  .live-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;

    .live-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h4 {
        margin: 0;
        color: #262626;
      }
    }
  }

  .live-content {
    display: flex;
    height: 400px;

    .player-area {
      flex: 1;
      position: relative;
      background: #000;

      .video-container {
        position: relative;
        width: 100%;
        height: 100%;

        .loading-overlay,
        .error-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.7);
          z-index: 10;
        }

        .video-element {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }

        .video-controls {
          position: absolute;
          bottom: 16px;
          left: 16px;
          right: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: rgba(0, 0, 0, 0.5);
          border-radius: 6px;
          padding: 8px 12px;
          opacity: 0;
          transition: opacity 0.3s ease;

          .control-button {
            color: #fff;
            border: none;
            
            &:hover {
              color: #1890ff;
              background: rgba(255, 255, 255, 0.1);
            }
          }
        }

        &:hover .video-controls {
          opacity: 1;
        }
      }

      .waiting-container,
      .ended-container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

        .waiting-content,
        .ended-content {
          text-align: center;
          color: #fff;

          h4 {
            color: #fff;
            margin-bottom: 8px;
          }
        }
      }
    }

    .chat-area {
      width: 300px;
      border-left: 1px solid #f0f0f0;
      display: flex;
      flex-direction: column;

      .chat-header {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        background: #fafafa;

        h5 {
          margin: 0;
          color: #262626;
        }
      }

      .chat-messages {
        flex: 1;
        padding: 16px;
        overflow-y: auto;
        background: #fff;
      }

      .chat-input {
        padding: 12px 16px;
        border-top: 1px solid #f0f0f0;
        background: #fafafa;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .live-player {
    .live-content {
      .chat-area {
        width: 250px;
      }
    }
  }
}

@media (max-width: 768px) {
  .live-player {
    .live-header {
      padding: 12px 16px;

      .live-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        h4 {
          font-size: 16px;
        }
      }
    }

    .live-content {
      flex-direction: column;
      height: auto;

      .player-area {
        height: 250px;
      }

      .chat-area {
        width: 100%;
        height: 200px;
        border-left: none;
        border-top: 1px solid #f0f0f0;
      }
    }
  }
}

@media (max-width: 480px) {
  .live-player {
    .live-header {
      padding: 8px 12px;

      .live-info {
        h4 {
          font-size: 14px;
        }
      }
    }

    .live-content {
      .player-area {
        height: 200px;

        .video-controls {
          bottom: 8px;
          left: 8px;
          right: 8px;
          padding: 6px 8px;

          .control-button {
            font-size: 14px;
          }
        }
      }

      .chat-area {
        height: 150px;

        .chat-header {
          padding: 8px 12px;

          h5 {
            font-size: 14px;
          }
        }

        .chat-messages {
          padding: 12px;
        }

        .chat-input {
          padding: 8px 12px;
        }
      }
    }
  }
}

// 全屏样式
:global(.live-player:fullscreen) {
  .player-area {
    height: 100vh !important;
  }

  .chat-area {
    display: none;
  }
}
