.scorm-player {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;

  .scorm-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .scorm-info {
      flex: 1;

      h4 {
        margin: 0 0 8px 0;
        color: #262626;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .ant-space {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .progress-bar {
    padding: 8px 20px;
    background: #f9f9f9;
    border-bottom: 1px solid #f0f0f0;

    .ant-progress {
      margin: 0;
    }
  }

  .scorm-container {
    position: relative;
    background: #fff;
    overflow: hidden;

    .loading-overlay,
    .error-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
      z-index: 10;
    }

    .scorm-iframe {
      display: block;
      border: none;
      width: 100%;
      height: 100%;
      background: #fff;
    }
  }

  // 全屏样式
  &:fullscreen {
    .scorm-container {
      height: 100vh !important;
    }

    .scorm-header {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background: rgba(250, 250, 250, 0.95);
      backdrop-filter: blur(10px);
    }

    .progress-bar {
      position: fixed;
      top: 60px;
      left: 0;
      right: 0;
      z-index: 1000;
      background: rgba(249, 249, 249, 0.95);
      backdrop-filter: blur(10px);
    }

    .scorm-iframe {
      margin-top: 100px;
      height: calc(100vh - 100px) !important;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .scorm-player {
    .scorm-header {
      padding: 12px 16px;
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .scorm-info {
        width: 100%;

        h4 {
          font-size: 16px;
          margin-bottom: 6px;
        }

        .ant-space {
          font-size: 11px;
          flex-wrap: wrap;
        }
      }
    }

    .progress-bar {
      padding: 6px 16px;
    }

    &:fullscreen {
      .scorm-header {
        padding: 8px 12px;
      }

      .progress-bar {
        top: 80px;
        padding: 4px 12px;
      }

      .scorm-iframe {
        margin-top: 120px;
        height: calc(100vh - 120px) !important;
      }
    }
  }
}

@media (max-width: 480px) {
  .scorm-player {
    .scorm-header {
      padding: 8px 12px;

      .scorm-info {
        h4 {
          font-size: 14px;
        }

        .ant-space {
          font-size: 10px;
        }
      }

      .ant-btn {
        font-size: 12px;
        height: 28px;
        padding: 0 8px;
      }
    }

    .progress-bar {
      padding: 4px 12px;
    }
  }
}

// SCORM 特定样式
.scorm-status {
  &.not-attempted {
    color: #8c8c8c;
  }

  &.incomplete {
    color: #faad14;
  }

  &.completed {
    color: #52c41a;
  }

  &.passed {
    color: #52c41a;
  }

  &.failed {
    color: #ff4d4f;
  }
}

// 学习进度指示器
.learning-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 20;

  &.active {
    background: rgba(24, 144, 255, 0.8);
  }

  &.completed {
    background: rgba(82, 196, 26, 0.8);
  }
}

// 错误状态样式
.error-state {
  .scorm-container {
    background: #fafafa;
  }

  .error-overlay {
    background: rgba(255, 255, 255, 0.95);
  }
}

// 加载状态样式
.loading-state {
  .scorm-iframe {
    opacity: 0.5;
    pointer-events: none;
  }
}
