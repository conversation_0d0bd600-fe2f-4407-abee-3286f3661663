import React, { useEffect, useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { Button, message, Spin, Alert } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { scorm } from "../../../api";
import styles from "./preview.module.less";

interface ScormPackageDetail {
  id: number;
  title: string;
  description: string;
  scorm_version: string;
  launch_file: string;
  extract_path: string;
  parse_status: number;
  parse_error?: string;
}

const ScormPreviewPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [packageDetail, setPackageDetail] = useState<ScormPackageDetail | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [launchUrl, setLaunchUrl] = useState<string>("");

  useEffect(() => {
    if (id) {
      getPackageDetail();
    }
  }, [id]);

  const getPackageDetail = async () => {
    try {
      setLoading(true);
      const res = await scorm.packageDetail(Number(id));
      const detail = res.data;
      
      if (detail.parse_status !== 1) {
        setError("SCORM包尚未解析完成或解析失败");
        setLoading(false);
        return;
      }

      setPackageDetail(detail);

      // 构建启动URL - 使用SCORM文件代理API
      const protocol = window.location.protocol;
      const hostname = window.location.hostname;
      const backendUrl = `${protocol}//${hostname}:9898`;
      const scormUrl = `${backendUrl}/storage/scorm/packages/${detail.id}/files/${detail.launch_file}`;
      setLaunchUrl(scormUrl);
      
      setLoading(false);
    } catch (err: any) {
      console.error("获取SCORM包详情失败:", err);
      setError(err.message || "获取SCORM包详情失败");
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  const handleReload = () => {
    window.location.reload();
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <Spin size="large">
            <div style={{ padding: '20px', textAlign: 'center' }}>
              加载SCORM包信息...
            </div>
          </Spin>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <Button 
            type="text" 
            icon={<ArrowLeftOutlined />} 
            onClick={handleBack}
            className={styles.backButton}
          >
            返回
          </Button>
          <h1>SCORM预览</h1>
        </div>
        <div className={styles.errorContainer}>
          <Alert
            message="加载失败"
            description={error}
            type="error"
            showIcon
            action={
              <Button size="small" onClick={handleReload}>
                重新加载
              </Button>
            }
          />
        </div>
      </div>
    );
  }

  if (!packageDetail) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <Button 
            type="text" 
            icon={<ArrowLeftOutlined />} 
            onClick={handleBack}
            className={styles.backButton}
          >
            返回
          </Button>
          <h1>SCORM预览</h1>
        </div>
        <div className={styles.errorContainer}>
          <Alert
            message="SCORM包不存在"
            type="warning"
            showIcon
          />
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Button 
          type="text" 
          icon={<ArrowLeftOutlined />} 
          onClick={handleBack}
          className={styles.backButton}
        >
          返回
        </Button>
        <div className={styles.titleInfo}>
          <h1>{packageDetail.title}</h1>
          <span className={styles.version}>SCORM {packageDetail.scorm_version}</span>
        </div>
      </div>
      
      <div className={styles.content}>
        <iframe
          src={launchUrl}
          width="100%"
          height="100%"
          frameBorder="0"
          className={styles.scormFrame}
          title={packageDetail.title}
          sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-downloads"
        />
      </div>
    </div>
  );
};

export default ScormPreviewPage;
