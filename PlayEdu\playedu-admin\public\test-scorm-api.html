<!DOCTYPE html>
<html>
<head>
    <title>SCORM API 测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 4px; white-space: pre-wrap; font-family: monospace; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <h1>SCORM API 测试</h1>
    
    <div>
        <button onclick="testBasicConnection()">测试基本连接</button>
        <button onclick="testScormAPI()">测试SCORM API</button>
        <button onclick="testWithCredentials()">测试带认证</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 测试基本连接
        async function testBasicConnection() {
            log('测试基本连接到后端服务...');
            
            try {
                // 测试一个简单的健康检查端点
                const response = await fetch('http://localhost:9898/backend/v1/system/config', {
                    method: 'GET',
                    mode: 'cors',
                    credentials: 'include'
                });
                
                log(`连接状态: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    log('✅ 后端服务连接正常', 'success');
                } else {
                    log(`❌ 后端服务响应异常: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 连接失败: ${error.message}`, 'error');
                log('可能的原因: 1) 后端服务未启动 2) CORS配置问题 3) 网络问题', 'error');
            }
        }

        // 测试SCORM API
        async function testScormAPI() {
            log('测试SCORM API...');
            
            try {
                const response = await fetch('http://localhost:9898/api/v1/scorm/packages/4/initialize?userId=1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    mode: 'cors',
                    credentials: 'include',
                    body: JSON.stringify({})
                });
                
                log(`SCORM API状态: ${response.status} ${response.statusText}`);
                
                const result = await response.json();
                log(`响应内容: ${JSON.stringify(result, null, 2)}`);
                
                if (result.code === 200) {
                    log('✅ SCORM API调用成功!', 'success');
                } else {
                    log(`❌ SCORM API调用失败: ${result.msg}`, 'error');
                }
            } catch (error) {
                log(`❌ SCORM API请求异常: ${error.message}`, 'error');
            }
        }

        // 测试带认证
        async function testWithCredentials() {
            log('测试带认证的SCORM API...');
            
            // 尝试获取认证信息
            const token = localStorage.getItem('playedu-pc-token') || 
                         sessionStorage.getItem('playedu-pc-token') ||
                         getCookie('playedu-pc-token');
            
            log(`认证Token: ${token ? '已找到' : '未找到'}`);
            
            try {
                const headers = {
                    'Content-Type': 'application/json',
                };
                
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }
                
                const response = await fetch('http://localhost:9898/api/v1/scorm/packages/4/initialize?userId=1', {
                    method: 'POST',
                    headers: headers,
                    mode: 'cors',
                    credentials: 'include',
                    body: JSON.stringify({})
                });
                
                log(`带认证API状态: ${response.status} ${response.statusText}`);
                
                const result = await response.json();
                log(`响应内容: ${JSON.stringify(result, null, 2)}`);
                
                if (result.code === 200) {
                    log('✅ 带认证的SCORM API调用成功!', 'success');
                } else {
                    log(`❌ 带认证的SCORM API调用失败: ${result.msg}`, 'error');
                }
            } catch (error) {
                log(`❌ 带认证的SCORM API请求异常: ${error.message}`, 'error');
            }
        }

        // 获取Cookie的辅助函数
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        // 页面加载时的说明
        window.onload = function() {
            log('SCORM API测试页面已加载');
            log('建议测试顺序:');
            log('1. 先测试基本连接');
            log('2. 再测试SCORM API');
            log('3. 如果失败，尝试带认证测试');
        };
    </script>
</body>
</html>
