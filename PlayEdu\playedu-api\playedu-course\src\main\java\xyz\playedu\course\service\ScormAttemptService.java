/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import xyz.playedu.course.domain.ScormAttempt;

import java.util.List;
import java.util.Map;

/**
 * SCORM学习尝试服务接口
 *
 * <AUTHOR> Team
 */
public interface ScormAttemptService extends IService<ScormAttempt> {

    /**
     * 根据用户ID和包ID获取学习尝试
     *
     * @param userId 用户ID
     * @param packageId 包ID
     * @return 学习尝试列表
     */
    List<ScormAttempt> getByUserAndPackage(Integer userId, Integer packageId);

    /**
     * 获取当前学习尝试
     *
     * @param userId 用户ID
     * @param packageId 包ID
     * @return 当前学习尝试
     */
    ScormAttempt getCurrentAttempt(Integer userId, Integer packageId);

    /**
     * 创建学习尝试
     *
     * @param userId 用户ID
     * @param packageId 包ID
     * @param hourId 课程小时ID
     * @return 创建的学习尝试
     */
    ScormAttempt createAttempt(Integer userId, Integer packageId, Integer hourId);

    /**
     * 更新学习尝试
     *
     * @param id 尝试ID
     * @param attempt 学习尝试信息
     * @return 更新的学习尝试
     */
    ScormAttempt updateAttempt(Integer id, ScormAttempt attempt);

    /**
     * 删除学习尝试
     *
     * @param id 尝试ID
     */
    void deleteAttempt(Integer id);

    /**
     * 更新学习进度
     *
     * @param attemptId 尝试ID
     * @param element 数据元素
     * @param value 值
     * @return 是否成功
     */
    boolean updateProgress(Integer attemptId, String element, String value);

    /**
     * 提交学习数据
     *
     * @param attemptId 尝试ID
     * @return 是否成功
     */
    boolean commitData(Integer attemptId);

    /**
     * 终止学习会话
     *
     * @param attemptId 尝试ID
     * @return 是否成功
     */
    boolean terminateSession(Integer attemptId);

    /**
     * 获取用户学习记录
     *
     * @param userId 用户ID
     * @param packageId SCORM包ID
     * @return 学习记录列表
     */
    List<ScormAttempt> getUserAttempts(Integer userId, Integer packageId);

    /**
     * 获取SCORM包学习统计
     *
     * @param packageId SCORM包ID
     * @return 统计数据
     */
    Map<String, Object> getPackageStatistics(Integer packageId);

    /**
     * 获取用户学习统计
     *
     * @param userId 用户ID
     * @return 统计数据
     */
    Map<String, Object> getUserStatistics(Integer userId);

    /**
     * 获取学习报告
     *
     * @param packageId SCORM包ID
     * @param page 页码
     * @param size 页大小
     * @return 学习报告
     */
    Map<String, Object> getLearningReport(Integer packageId, Integer page, Integer size);

    /**
     * 导出学习数据
     *
     * @param packageId SCORM包ID
     * @return 导出数据
     */
    List<Map<String, Object>> exportLearningData(Integer packageId);

    /**
     * 计算学习进度
     *
     * @param attempt 学习尝试
     * @return 进度百分比
     */
    Double calculateProgress(ScormAttempt attempt);

    /**
     * 判断是否完成学习
     *
     * @param attempt 学习尝试
     * @return 是否完成
     */
    boolean isCompleted(ScormAttempt attempt);

    /**
     * 判断是否通过
     *
     * @param attempt 学习尝试
     * @return 是否通过
     */
    boolean isPassed(ScormAttempt attempt);
}
