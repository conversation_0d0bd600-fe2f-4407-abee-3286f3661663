import client from "./internal/httpClient";

// 获取SCORM包列表
export function packageList(
  page: number,
  size: number,
  title?: string
) {
  return client.get("/backend/v1/scorm/packages", {
    page,
    size,
    title,
  });
}

// 获取SCORM包统计信息
export function packageStatistics() {
  return client.get("/backend/v1/scorm/packages/statistics");
}

// 获取SCORM包详情
export function packageDetail(id: number) {
  return client.get(`/backend/v1/scorm/packages/${id}`);
}

// 删除SCORM包
export function destroyPackage(id: number) {
  return client.delete(`/backend/v1/scorm/packages/${id}`);
}

// 重新解析SCORM包
export function reparsePackage(id: number) {
  return client.post(`/backend/v1/scorm/packages/${id}/reparse`);
}

// 获取学习尝试列表
export function attemptList(
  page: number,
  size: number,
  packageId?: number,
  userId?: number
) {
  return client.get("/backend/v1/scorm/attempts", {
    page,
    size,
    package_id: packageId,
    user_id: userId,
  });
}

// 获取学习尝试详情
export function attemptDetail(id: number) {
  return client.get(`/backend/v1/scorm/attempts/${id}`);
}

// 获取学习统计报告
export function learningReport(packageId: number) {
  return client.get(`/backend/v1/scorm/packages/${packageId}/report`);
}

// 导出学习数据
export function exportLearningData(packageId: number) {
  return client.get(`/backend/v1/scorm/packages/${packageId}/export`, {
    responseType: 'blob'
  });
}

// SCORM 播放器 API
export function initialize(packageId: number, hourId?: number) {
  const params = hourId ? { hour_id: hourId } : {};
  return client.post(`/api/v1/scorm/packages/${packageId}/initialize`, params);
}

export function getData(attemptId: number, element: string) {
  return client.get(`/api/v1/scorm/attempts/${attemptId}/data`, {
    params: { element }
  });
}

export function setData(attemptId: number, element: string, value: string) {
  return client.post(`/api/v1/scorm/attempts/${attemptId}/data`, {
    element,
    value
  });
}

export function commit(attemptId: number) {
  return client.post(`/api/v1/scorm/attempts/${attemptId}/commit`);
}

export function terminate(attemptId: number) {
  return client.post(`/api/v1/scorm/attempts/${attemptId}/terminate`);
}

export function getContent(packageId: number, contentPath: string) {
  return client.get(`/api/v1/scorm/packages/${packageId}/content/${contentPath}`);
}
