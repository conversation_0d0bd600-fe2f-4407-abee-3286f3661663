/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.resource.service;

import xyz.playedu.resource.domain.Resource;

/**
 * 文档预览服务接口
 * 
 * <AUTHOR> Team
 */
public interface DocumentPreviewService {

    /**
     * 获取文档预览URL
     * 
     * @param resource 资源对象
     * @return 预览URL
     */
    String getPreviewUrl(Resource resource);

    /**
     * 检查文档是否支持预览
     * 
     * @param extension 文件扩展名
     * @return 是否支持预览
     */
    boolean isSupportPreview(String extension);

    /**
     * 转换文档为PDF
     * 
     * @param resource 资源对象
     * @return 转换后的PDF资源ID
     */
    Integer convertToPdf(Resource resource);

    /**
     * 获取文档页数
     * 
     * @param resource 资源对象
     * @return 页数
     */
    Integer getPageCount(Resource resource);

    /**
     * 获取文档缩略图
     * 
     * @param resource 资源对象
     * @param page 页码
     * @return 缩略图URL
     */
    String getThumbnail(Resource resource, Integer page);

    /**
     * 获取Office在线预览URL（使用Office Online或类似服务）
     * 
     * @param resource 资源对象
     * @return 在线预览URL
     */
    String getOfficeOnlineUrl(Resource resource);

    /**
     * 获取PDF.js预览URL
     * 
     * @param resource 资源对象
     * @return PDF.js预览URL
     */
    String getPdfJsUrl(Resource resource);
}
