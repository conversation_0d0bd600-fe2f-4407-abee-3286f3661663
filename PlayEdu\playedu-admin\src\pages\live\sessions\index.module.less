.live-sessions-page {
  .statistics-cards {
    margin-bottom: 24px;
    
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      
      .ant-statistic-title {
        color: #666;
        font-size: 14px;
      }
      
      .ant-statistic-content {
        font-size: 24px;
        font-weight: 600;
      }
    }
  }
  
  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .left-actions {
      .ant-btn {
        margin-right: 8px;
      }
    }
    
    .right-filters {
      .ant-select,
      .ant-input-search {
        margin-left: 8px;
      }
    }
  }
  
  .live-table {
    .ant-table {
      border-radius: 8px;
      overflow: hidden;
      
      .ant-table-thead > tr > th {
        background: #fafafa;
        border-bottom: 1px solid #f0f0f0;
        font-weight: 600;
      }
      
      .ant-table-tbody > tr > td {
        border-bottom: 1px solid #f0f0f0;
      }
      
      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }
    
    .status-badge {
      .ant-badge-status-dot {
        width: 8px;
        height: 8px;
      }
    }
    
    .time-info {
      font-size: 12px;
      color: #666;
      
      > div {
        margin-bottom: 2px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    
    .viewer-info {
      font-size: 12px;
      
      > div {
        margin-bottom: 2px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    
    .action-buttons {
      .ant-btn {
        margin-right: 4px;
        
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.live-form-modal {
  .ant-form-item {
    margin-bottom: 16px;
  }
  
  .ant-form-item-label > label {
    font-weight: 500;
  }
  
  .time-range-picker {
    width: 100%;
  }
  
  .upload-area {
    .ant-upload-select {
      width: 100px;
      height: 100px;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      background: #fafafa;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        border-color: #1890ff;
        background: #f0f8ff;
      }
      
      .anticon {
        font-size: 24px;
        color: #999;
        margin-bottom: 8px;
      }
      
      .upload-text {
        font-size: 12px;
        color: #666;
      }
    }
  }
  
  .form-actions {
    text-align: right;
    margin-top: 24px;
    
    .ant-btn {
      margin-left: 8px;
    }
  }
}

@media (max-width: 768px) {
  .live-sessions-page {
    .operation-bar {
      flex-direction: column;
      align-items: stretch;
      
      .left-actions {
        margin-bottom: 16px;
      }
      
      .right-filters {
        .ant-select,
        .ant-input-search {
          margin-left: 0;
          margin-top: 8px;
          width: 100%;
        }
      }
    }
    
    .statistics-cards {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }
}
