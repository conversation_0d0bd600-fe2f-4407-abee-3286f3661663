<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.common.mapper.AdminLogMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.common.domain.AdminLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="adminId" column="admin_id" jdbcType="INTEGER"/>
            <result property="adminName" column="admin_name" jdbcType="VARCHAR"/>
            <result property="module" column="module" jdbcType="VARCHAR"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="opt" column="opt" jdbcType="INTEGER"/>
            <result property="method" column="method" jdbcType="VARCHAR"/>
            <result property="requestMethod" column="request_method" jdbcType="VARCHAR"/>
            <result property="url" column="url" jdbcType="VARCHAR"/>
            <result property="param" column="param" jdbcType="VARCHAR"/>
            <result property="result" column="result" jdbcType="VARCHAR"/>
            <result property="ip" column="ip" jdbcType="VARCHAR"/>
            <result property="ipArea" column="ip_area" jdbcType="VARCHAR"/>
            <result property="errorMsg" column="error_msg" jdbcType="VARCHAR"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,admin_id,admin_name,module,title,
        opt,method,request_method,url,param,result,
        ip,ip_area,error_msg,created_at
    </sql>

    <select id="paginate" resultType="xyz.playedu.common.domain.AdminLog">
        SELECT `admin_logs`.`id`,`admin_logs`.`admin_id`,`admin_logs`.`admin_name`,`admin_logs`.`module`,`admin_logs`.`title`,
        `admin_logs`.`opt`,`admin_logs`.`method`,`admin_logs`.`request_method`,`admin_logs`.`url`,`admin_logs`.`ip`,`admin_logs`.`param`,
        `admin_logs`.`ip_area`,`admin_logs`.`created_at`
        FROM `admin_logs`
        <where>
            <if test="adminId != null">
                AND `admin_logs`.`admin_id` = #{adminId}
            </if>
            <if test="adminName != null and adminName != ''">
                AND `admin_logs`.`admin_name` LIKE concat('%',#{adminName},'%')
            </if>
            <if test="module != null and module != ''">
                AND `admin_logs`.`module` LIKE concat('%',#{module},'%')
            </if>
            <if test="title != null and title != ''">
                AND `admin_logs`.`title` LIKE concat('%',#{title},'%')
            </if>
            <if test="opt != null">
                AND `admin_logs`.`opt` = #{opt}
            </if>
            <if test="startTime != null and startTime != '' ">
                <if test="endTime != null and endTime != ''">
                    AND `admin_logs`.`created_at` BETWEEN #{startTime} AND #{endTime}
                </if>
            </if>

        </where>

        <if test="sortAlgo == 'asc'">
            <choose>
                <when test="sortField == 'created_at'">
                    ORDER BY `admin_logs`.`created_at` ASC
                </when>
                <otherwise>
                    ORDER BY `admin_logs`.`id` ASC
                </otherwise>
            </choose>
        </if>
        <if test="sortAlgo != 'asc'">
            <choose>
                <when test="sortField == 'created_at'">
                    ORDER BY `admin_logs`.`created_at` DESC
                </when>
                <otherwise>
                    ORDER BY `admin_logs`.`id` DESC
                </otherwise>
            </choose>
        </if>
        LIMIT #{pageStart}, #{pageSize};
    </select>

    <select id="paginateCount" resultType="java.lang.Long">
        SELECT count(1)
        FROM `admin_logs`
        <where>
            <if test="adminId != null">
                AND `admin_logs`.`admin_id` = #{adminId}
            </if>
            <if test="adminName != null and adminName != ''">
                AND `admin_logs`.`admin_name` LIKE concat('%',#{adminName},'%')
            </if>
            <if test="module != null and module != ''">
                AND `admin_logs`.`module` LIKE concat('%',#{module},'%')
            </if>
            <if test="title != null and title != ''">
                AND `admin_logs`.`title` LIKE concat('%',#{title},'%')
            </if>
            <if test="opt != null">
                AND `admin_logs`.`opt` = #{opt}
            </if>
            <if test="startTime != null and startTime != '' ">
                <if test="endTime != null and endTime != ''">
                    AND `admin_logs`.`created_at` BETWEEN #{startTime} AND #{endTime}
                </if>
            </if>
        </where>
    </select>
</mapper>
