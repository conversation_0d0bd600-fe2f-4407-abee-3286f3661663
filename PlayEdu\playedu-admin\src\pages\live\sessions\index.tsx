import { useEffect, useState } from "react";
import {
  Table,
  Button,
  Space,
  Modal,
  message,
  Input,
  Select,
  Badge,
  Tag,
  Tooltip,
  Card,
  Statistic,
  Row,
  Col,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  StopOutlined,
  EyeOutlined,
  ExclamationCircleFilled,
  LiveOutlined,
  UserOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { live } from "../../../api/index";
import { CreateLiveSession } from "./components/create";
import { UpdateLiveSession } from "./components/update";
import styles from "./index.module.less";

const { Search } = Input;
const { Option } = Select;
const { confirm } = Modal;

interface LiveSessionItem {
  id: number;
  title: string;
  description: string;
  status: number; // 0-未开始，1-直播中，2-已结束
  start_time: string;
  end_time: string;
  actual_start_time?: string;
  actual_end_time?: string;
  max_viewers: number;
  current_viewers: number;
  is_record: number;
  is_playback: number;
  created_at: string;
  admin_name: string;
}

const LiveSessionsPage = () => {
  const [list, setList] = useState<LiveSessionItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [refresh, setRefresh] = useState(false);
  const [keywords, setKeywords] = useState("");
  const [status, setStatus] = useState<number | undefined>(undefined);
  const [createVisible, setCreateVisible] = useState(false);
  const [updateVisible, setUpdateVisible] = useState(false);
  const [updateId, setUpdateId] = useState(0);
  const [statistics, setStatistics] = useState({
    total: 0,
    living: 0,
    upcoming: 0,
    ended: 0,
  });

  useEffect(() => {
    getData();
    getStatistics();
  }, [page, size, refresh, keywords, status]);

  const getData = () => {
    setLoading(true);
    live
      .sessionList(page, size, keywords, status)
      .then((res: any) => {
        setList(res.data.data);
        setTotal(res.data.total);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const getStatistics = () => {
    live.sessionStatistics().then((res: any) => {
      setStatistics(res.data);
    });
  };

  const resetData = () => {
    setPage(1);
    setList([]);
    setRefresh(!refresh);
  };

  const startLive = (id: number) => {
    confirm({
      title: "开始直播",
      content: "确认开始此直播吗？",
      onOk() {
        live.startSession(id).then(() => {
          message.success("直播已开始");
          resetData();
        });
      },
    });
  };

  const endLive = (id: number) => {
    confirm({
      title: "结束直播",
      content: "确认结束此直播吗？",
      onOk() {
        live.endSession(id).then(() => {
          message.success("直播已结束");
          resetData();
        });
      },
    });
  };

  const deleteLive = (id: number) => {
    confirm({
      title: "删除直播",
      icon: <ExclamationCircleFilled />,
      content: "确认删除此直播吗？删除后无法恢复。",
      onOk() {
        live.destroySession(id).then(() => {
          message.success("删除成功");
          resetData();
        });
      },
    });
  };

  const getStatusBadge = (status: number) => {
    switch (status) {
      case 0:
        return <Badge status="default" text="未开始" />;
      case 1:
        return <Badge status="processing" text="直播中" />;
      case 2:
        return <Badge status="error" text="已结束" />;
      default:
        return <Badge status="default" text="未知" />;
    }
  };

  const getStatusColor = (status: number) => {
    switch (status) {
      case 0:
        return "default";
      case 1:
        return "processing";
      case 2:
        return "error";
      default:
        return "default";
    }
  };

  const columns = [
    {
      title: "直播标题",
      dataIndex: "title",
      key: "title",
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: number) => getStatusBadge(status),
    },
    {
      title: "计划时间",
      key: "schedule_time",
      width: 180,
      render: (record: LiveSessionItem) => (
        <div>
          <div>开始: {record.start_time}</div>
          <div>结束: {record.end_time}</div>
        </div>
      ),
    },
    {
      title: "实际时间",
      key: "actual_time",
      width: 180,
      render: (record: LiveSessionItem) => (
        <div>
          <div>开始: {record.actual_start_time || "-"}</div>
          <div>结束: {record.actual_end_time || "-"}</div>
        </div>
      ),
    },
    {
      title: "观看人数",
      key: "viewers",
      width: 120,
      render: (record: LiveSessionItem) => (
        <div>
          <div>当前: {record.current_viewers}</div>
          <div>最大: {record.max_viewers}</div>
        </div>
      ),
    },
    {
      title: "录制",
      dataIndex: "is_record",
      key: "is_record",
      width: 80,
      render: (is_record: number) => (
        <Tag color={is_record ? "green" : "default"}>
          {is_record ? "是" : "否"}
        </Tag>
      ),
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      key: "created_at",
      width: 150,
    },
    {
      title: "操作",
      key: "action",
      width: 200,
      fixed: "right" as const,
      render: (record: LiveSessionItem) => (
        <Space size="small">
          {record.status === 0 && (
            <Button
              type="primary"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => startLive(record.id)}
            >
              开始
            </Button>
          )}
          {record.status === 1 && (
            <Button
              danger
              size="small"
              icon={<StopOutlined />}
              onClick={() => endLive(record.id)}
            >
              结束
            </Button>
          )}
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setUpdateId(record.id);
              setUpdateVisible(true);
            }}
          >
            编辑
          </Button>
          <Button
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => deleteLive(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="playedu-main-body">
      <div className="playedu-main-title">直播管理</div>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-24">
        <Col span={6}>
          <Card>
            <Statistic
              title="总直播数"
              value={statistics.total}
              prefix={<LiveOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="直播中"
              value={statistics.living}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: "#1890ff" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="即将开始"
              value={statistics.upcoming}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: "#faad14" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已结束"
              value={statistics.ended}
              prefix={<StopOutlined />}
              valueStyle={{ color: "#52c41a" }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作栏 */}
      <div className="playedu-main-top mb-30">
        <div className="playedu-main-top-left">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateVisible(true)}
          >
            创建直播
          </Button>
        </div>
        <div className="playedu-main-top-right">
          <Space>
            <Select
              style={{ width: 120 }}
              placeholder="状态筛选"
              allowClear
              value={status}
              onChange={(value) => {
                setStatus(value);
                setPage(1);
              }}
            >
              <Option value={0}>未开始</Option>
              <Option value={1}>直播中</Option>
              <Option value={2}>已结束</Option>
            </Select>
            <Search
              placeholder="搜索直播标题"
              style={{ width: 200 }}
              onSearch={(value) => {
                setKeywords(value);
                setPage(1);
              }}
              allowClear
            />
          </Space>
        </div>
      </div>

      {/* 表格 */}
      <div className="playedu-main-table">
        <Table
          columns={columns}
          dataSource={list}
          loading={loading}
          pagination={{
            current: page,
            pageSize: size,
            total: total,
            onChange: (page, pageSize) => {
              setPage(page);
              setSize(pageSize || 10);
            },
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          rowKey="id"
          scroll={{ x: 1200 }}
        />
      </div>

      {/* 创建直播弹窗 */}
      {createVisible && (
        <CreateLiveSession
          open={createVisible}
          onCancel={() => setCreateVisible(false)}
          onSuccess={() => {
            setCreateVisible(false);
            resetData();
          }}
        />
      )}

      {/* 编辑直播弹窗 */}
      {updateVisible && (
        <UpdateLiveSession
          id={updateId}
          open={updateVisible}
          onCancel={() => setUpdateVisible(false)}
          onSuccess={() => {
            setUpdateVisible(false);
            resetData();
          }}
        />
      )}
    </div>
  );
};

export default LiveSessionsPage;
