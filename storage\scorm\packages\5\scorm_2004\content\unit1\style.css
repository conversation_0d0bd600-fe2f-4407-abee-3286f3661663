/**
 * SCORM 2004 第一单元样式文件
 */

/* 单元头部样式 */
.unit-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.header-content h1 {
    margin: 0;
    font-size: 2.5em;
    font-weight: 300;
}

.unit-info {
    display: flex;
    gap: 20px;
    font-size: 0.9em;
}

.unit-duration,
.unit-difficulty {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

/* 进度条样式 */
.progress-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.progress-bar {
    flex: 1;
    height: 12px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    border-radius: 6px;
    transition: width 0.5s ease;
    width: 0%;
}

.progress-text {
    font-weight: bold;
    font-size: 1.1em;
    min-width: 40px;
}

/* 单元导航样式 */
.unit-navigation {
    display: flex;
    background: white;
    border-radius: 12px;
    padding: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 10px;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    min-width: 120px;
}

.nav-item:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
}

.nav-item.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.nav-icon {
    font-size: 1.5em;
    margin-bottom: 8px;
}

.nav-text {
    font-size: 0.9em;
    font-weight: 500;
    text-align: center;
    margin-bottom: 5px;
}

.nav-status {
    font-size: 1.2em;
    color: #bdc3c7;
    transition: color 0.3s ease;
}

/* 内容区域样式 */
.unit-content {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.content-section {
    display: none;
    padding: 40px;
    min-height: 600px;
}

.content-section.active {
    display: block;
    animation: fadeInUp 0.6s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content-section h2 {
    color: #2c3e50;
    font-size: 2.2em;
    margin-bottom: 30px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 15px;
    position: relative;
}

.content-section h2::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

/* 概览网格样式 */
.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.overview-card {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    border-left: 5px solid #3498db;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.overview-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-icon {
    font-size: 2.5em;
    margin-bottom: 15px;
}

.overview-card h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.overview-card ul {
    list-style: none;
    padding: 0;
}

.overview-card li {
    padding: 5px 0;
    position: relative;
    padding-left: 20px;
}

.overview-card li::before {
    content: "▸";
    position: absolute;
    left: 0;
    color: #3498db;
    font-weight: bold;
}

/* 学习目标样式 */
.objectives-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin-bottom: 30px;
}

.objective-item {
    display: flex;
    gap: 20px;
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.objective-item:hover {
    transform: translateX(10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.objective-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5em;
    font-weight: bold;
    flex-shrink: 0;
}

.objective-content h4 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.objective-content p {
    color: #666;
    margin-bottom: 15px;
    line-height: 1.6;
}

.objective-skills {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.skill-tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.85em;
    font-weight: 500;
}

/* 内容标签页样式 */
.content-tabs {
    margin-bottom: 30px;
}

.tab-nav {
    display: flex;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 5px;
    margin-bottom: 20px;
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    white-space: nowrap;
}

.tab-btn:hover {
    background: #e9ecef;
}

.tab-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.tab-content {
    min-height: 400px;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
    animation: fadeIn 0.4s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 内容媒体样式 */
.content-with-media {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    align-items: start;
}

.highlight-box {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #2196f3;
    margin: 20px 0;
}

.highlight-box h4 {
    color: #1565c0;
    margin-bottom: 15px;
}

.video-placeholder {
    background: #f5f5f5;
    border: 2px dashed #ccc;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.video-placeholder:hover {
    background: #eeeeee;
    border-color: #999;
}

.play-button {
    width: 60px;
    height: 60px;
    background: #3498db;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5em;
    margin: 0 auto 15px;
    transition: transform 0.3s ease;
}

.play-button:hover {
    transform: scale(1.1);
}

/* 特性网格样式 */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.feature-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
    text-align: center;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: #3498db;
}

.feature-icon {
    font-size: 2.5em;
    margin-bottom: 15px;
}

.feature-card h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

/* 架构图样式 */
.architecture-diagram {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 15px;
}

.arch-layer {
    background: white;
    padding: 20px 40px;
    border-radius: 10px;
    border: 2px solid #3498db;
    text-align: center;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.arch-layer h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.arch-arrow {
    font-size: 2em;
    color: #3498db;
    font-weight: bold;
}

/* 优势对比样式 */
.benefits-comparison {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.benefit-item {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 25px;
    border-radius: 12px;
    border-top: 4px solid #27ae60;
}

.benefit-item h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.benefit-item ul {
    list-style: none;
    padding: 0;
}

.benefit-item li {
    padding: 8px 0;
    position: relative;
    padding-left: 25px;
}

.benefit-item li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #27ae60;
    font-weight: bold;
}

/* 练习活动样式 */
.practice-container {
    margin-bottom: 30px;
}

.practice-intro {
    background: #e8f5e8;
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #4caf50;
    margin-bottom: 30px;
}

.practice-activity {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 25px;
    border: 1px solid #dee2e6;
}

.practice-activity h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3em;
}

/* 匹配游戏样式 */
.matching-game {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin: 20px 0;
}

.matching-left,
.matching-right {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.match-item {
    background: #3498db;
    color: white;
    padding: 15px;
    border-radius: 8px;
    cursor: grab;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
}

.match-item:hover {
    background: #2980b9;
    transform: scale(1.05);
}

.match-item:active {
    cursor: grabbing;
}

.match-target {
    background: white;
    border: 2px dashed #bdc3c7;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.match-target:hover {
    border-color: #3498db;
    background: #f8f9fa;
}

/* 排序游戏样式 */
.sorting-game {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 20px 0;
}

.sort-item {
    background: #e74c3c;
    color: white;
    padding: 15px;
    border-radius: 8px;
    cursor: grab;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
}

.sort-item:hover {
    background: #c0392b;
    transform: translateX(10px);
}

.sort-item:active {
    cursor: grabbing;
}

/* 活动结果样式 */
.activity-result {
    margin-top: 20px;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.result-text {
    margin: 0;
    font-weight: 500;
}

/* 评估样式 */
.assessment-container {
    margin-bottom: 30px;
}

.assessment-info {
    display: flex;
    justify-content: space-around;
    background: #e3f2fd;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
}

.info-item {
    text-align: center;
}

.info-label {
    display: block;
    font-size: 0.9em;
    color: #666;
    margin-bottom: 5px;
}

.info-value {
    font-size: 1.2em;
    font-weight: bold;
    color: #1976d2;
}

/* 测验样式 */
.quiz-question {
    background: white;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quiz-question h4 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.1em;
    line-height: 1.5;
}

.quiz-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.quiz-option {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.quiz-option:hover {
    background: #e9ecef;
    border-color: #3498db;
}

.quiz-option input[type="radio"] {
    margin-right: 12px;
    transform: scale(1.2);
}

/* 测验操作样式 */
.quiz-actions {
    text-align: center;
    margin: 30px 0;
}

/* 评估结果样式 */
.assessment-result {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    border: 1px solid #dee2e6;
    margin-top: 25px;
}

.assessment-result h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
}

.result-summary {
    display: flex;
    justify-content: space-around;
    margin-bottom: 25px;
    padding: 20px;
    background: white;
    border-radius: 10px;
}

.result-item {
    text-align: center;
}

.result-label {
    display: block;
    font-size: 0.9em;
    color: #666;
    margin-bottom: 8px;
}

.result-value {
    font-size: 1.5em;
    font-weight: bold;
}

/* 反馈样式 */
.feedback-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 20px;
    border-radius: 10px;
}

.feedback-retry {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 20px;
    border-radius: 10px;
}

.feedback-success h4,
.feedback-retry h4 {
    margin-bottom: 15px;
}

/* 章节操作样式 */
.section-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #dee2e6;
}

/* 单元页脚样式 */
.unit-footer {
    background: #2c3e50;
    color: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.scorm-status {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    text-align: center;
}

.status-item {
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.status-label {
    display: block;
    font-size: 0.9em;
    opacity: 0.8;
    margin-bottom: 8px;
}

.status-value {
    font-size: 1.1em;
    font-weight: bold;
    color: #3498db;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .unit-header {
        padding: 20px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .header-content h1 {
        font-size: 2em;
    }
    
    .unit-info {
        justify-content: center;
    }
    
    .unit-navigation {
        flex-direction: column;
        gap: 10px;
    }
    
    .nav-item {
        flex-direction: row;
        justify-content: space-between;
        text-align: left;
    }
    
    .content-section {
        padding: 20px;
    }
    
    .content-section h2 {
        font-size: 1.8em;
    }
    
    .overview-grid {
        grid-template-columns: 1fr;
    }
    
    .objective-item {
        flex-direction: column;
        text-align: center;
    }
    
    .content-with-media {
        grid-template-columns: 1fr;
    }
    
    .tab-nav {
        flex-direction: column;
    }
    
    .matching-game {
        grid-template-columns: 1fr;
    }
    
    .assessment-info {
        flex-direction: column;
        gap: 15px;
    }
    
    .result-summary {
        flex-direction: column;
        gap: 15px;
    }
    
    .scorm-status {
        grid-template-columns: 1fr;
    }
    
    .section-actions {
        flex-direction: column;
    }
}
