<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第一单元：基础概念与理论</title>
    <link rel="stylesheet" href="../../shared/common.css">
    <link rel="stylesheet" href="style.css">
    <script src="../../shared/scorm2004_api_wrapper.js"></script>
</head>
<body>
    <div class="container">
        <header class="unit-header">
            <div class="header-content">
                <h1>第一单元：基础概念与理论</h1>
                <div class="unit-info">
                    <span class="unit-duration">预计学习时间：60分钟</span>
                    <span class="unit-difficulty">难度：基础</span>
                </div>
            </div>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <span class="progress-text" id="progressText">0%</span>
            </div>
        </header>

        <nav class="unit-navigation">
            <div class="nav-item" data-section="overview">
                <span class="nav-icon">📋</span>
                <span class="nav-text">单元概览</span>
                <span class="nav-status" id="overviewStatus">●</span>
            </div>
            <div class="nav-item" data-section="objectives">
                <span class="nav-icon">🎯</span>
                <span class="nav-text">学习目标</span>
                <span class="nav-status" id="objectivesStatus">○</span>
            </div>
            <div class="nav-item" data-section="content">
                <span class="nav-icon">📚</span>
                <span class="nav-text">核心内容</span>
                <span class="nav-status" id="contentStatus">○</span>
            </div>
            <div class="nav-item" data-section="practice">
                <span class="nav-icon">✍️</span>
                <span class="nav-text">练习活动</span>
                <span class="nav-status" id="practiceStatus">○</span>
            </div>
            <div class="nav-item" data-section="assessment">
                <span class="nav-icon">📝</span>
                <span class="nav-text">知识检测</span>
                <span class="nav-status" id="assessmentStatus">○</span>
            </div>
        </nav>

        <main class="unit-content">
            <!-- 单元概览 -->
            <section id="overview" class="content-section active">
                <h2>单元概览</h2>
                <div class="overview-grid">
                    <div class="overview-card">
                        <div class="card-icon">🎓</div>
                        <h3>学习内容</h3>
                        <p>本单元将介绍SCORM 2004的基础概念、核心原理和技术架构，为后续深入学习打下坚实基础。</p>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">⏱️</div>
                        <h3>时间安排</h3>
                        <ul>
                            <li>理论学习：30分钟</li>
                            <li>实践练习：20分钟</li>
                            <li>知识检测：10分钟</li>
                        </ul>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">📊</div>
                        <h3>评估标准</h3>
                        <ul>
                            <li>完成所有学习内容</li>
                            <li>参与练习活动</li>
                            <li>通过知识检测（≥75分）</li>
                        </ul>
                    </div>
                </div>
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="nextSection()">开始学习</button>
                </div>
            </section>

            <!-- 学习目标 -->
            <section id="objectives" class="content-section">
                <h2>学习目标</h2>
                <div class="objectives-container">
                    <div class="objective-item">
                        <div class="objective-number">1</div>
                        <div class="objective-content">
                            <h4>理解SCORM 2004标准</h4>
                            <p>掌握SCORM 2004的基本概念、发展历程和主要特性</p>
                            <div class="objective-skills">
                                <span class="skill-tag">概念理解</span>
                                <span class="skill-tag">标准认知</span>
                            </div>
                        </div>
                    </div>
                    <div class="objective-item">
                        <div class="objective-number">2</div>
                        <div class="objective-content">
                            <h4>掌握技术架构</h4>
                            <p>了解SCORM 2004的技术架构、组件关系和工作原理</p>
                            <div class="objective-skills">
                                <span class="skill-tag">架构分析</span>
                                <span class="skill-tag">技术理解</span>
                            </div>
                        </div>
                    </div>
                    <div class="objective-item">
                        <div class="objective-number">3</div>
                        <div class="objective-content">
                            <h4>应用实践能力</h4>
                            <p>能够运用所学知识分析和解决实际问题</p>
                            <div class="objective-skills">
                                <span class="skill-tag">实践应用</span>
                                <span class="skill-tag">问题解决</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="section-actions">
                    <button class="btn btn-secondary" onclick="prevSection()">上一页</button>
                    <button class="btn btn-primary" onclick="nextSection()">下一页</button>
                </div>
            </section>

            <!-- 核心内容 -->
            <section id="content" class="content-section">
                <h2>核心内容</h2>
                <div class="content-tabs">
                    <div class="tab-nav">
                        <button class="tab-btn active" data-tab="intro">SCORM简介</button>
                        <button class="tab-btn" data-tab="features">主要特性</button>
                        <button class="tab-btn" data-tab="architecture">技术架构</button>
                        <button class="tab-btn" data-tab="benefits">优势分析</button>
                    </div>
                    
                    <div class="tab-content">
                        <div id="intro" class="tab-panel active">
                            <h3>SCORM 2004简介</h3>
                            <div class="content-with-media">
                                <div class="text-content">
                                    <p><strong>SCORM</strong>（Sharable Content Object Reference Model）是由美国国防部高级分布式学习倡议（ADL）开发的电子学习标准。</p>
                                    <p>SCORM 2004是该标准的第四版，也被称为SCORM 2004 4th Edition，它在前版本基础上增加了更强大的排序和导航功能。</p>
                                    <div class="highlight-box">
                                        <h4>核心理念</h4>
                                        <ul>
                                            <li><strong>可重用性</strong>：内容可在不同平台间重复使用</li>
                                            <li><strong>可访问性</strong>：学习者可随时随地访问内容</li>
                                            <li><strong>互操作性</strong>：不同系统间可以互相操作</li>
                                            <li><strong>持久性</strong>：技术升级不影响内容使用</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="media-content">
                                    <div class="video-placeholder">
                                        <div class="play-button" onclick="playIntroVideo()">▶</div>
                                        <p>点击播放介绍视频</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div id="features" class="tab-panel">
                            <h3>SCORM 2004主要特性</h3>
                            <div class="features-grid">
                                <div class="feature-card">
                                    <div class="feature-icon">🔄</div>
                                    <h4>高级排序</h4>
                                    <p>支持复杂的学习路径控制和自适应学习序列</p>
                                </div>
                                <div class="feature-card">
                                    <div class="feature-icon">🧭</div>
                                    <h4>导航控制</h4>
                                    <p>提供灵活的导航选项和用户界面控制</p>
                                </div>
                                <div class="feature-card">
                                    <div class="feature-icon">📊</div>
                                    <h4>数据跟踪</h4>
                                    <p>详细的学习数据收集和分析能力</p>
                                </div>
                                <div class="feature-card">
                                    <div class="feature-icon">🎯</div>
                                    <h4>目标管理</h4>
                                    <p>支持学习目标的定义和跟踪</p>
                                </div>
                            </div>
                        </div>
                        
                        <div id="architecture" class="tab-panel">
                            <h3>技术架构</h3>
                            <div class="architecture-diagram">
                                <div class="arch-layer">
                                    <h4>内容聚合模型 (CAM)</h4>
                                    <p>定义内容包装和元数据标准</p>
                                </div>
                                <div class="arch-arrow">↓</div>
                                <div class="arch-layer">
                                    <h4>运行时环境 (RTE)</h4>
                                    <p>提供内容与LMS的通信接口</p>
                                </div>
                                <div class="arch-arrow">↓</div>
                                <div class="arch-layer">
                                    <h4>排序和导航 (SN)</h4>
                                    <p>控制学习活动的顺序和导航</p>
                                </div>
                            </div>
                        </div>
                        
                        <div id="benefits" class="tab-panel">
                            <h3>优势分析</h3>
                            <div class="benefits-comparison">
                                <div class="benefit-item">
                                    <h4>对学习者</h4>
                                    <ul>
                                        <li>个性化学习路径</li>
                                        <li>学习进度跟踪</li>
                                        <li>跨平台学习体验</li>
                                    </ul>
                                </div>
                                <div class="benefit-item">
                                    <h4>对教育机构</h4>
                                    <ul>
                                        <li>降低开发成本</li>
                                        <li>提高内容重用率</li>
                                        <li>标准化管理</li>
                                    </ul>
                                </div>
                                <div class="benefit-item">
                                    <h4>对开发者</h4>
                                    <ul>
                                        <li>统一开发标准</li>
                                        <li>丰富的API支持</li>
                                        <li>广泛的兼容性</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="section-actions">
                    <button class="btn btn-secondary" onclick="prevSection()">上一页</button>
                    <button class="btn btn-primary" onclick="nextSection()">下一页</button>
                </div>
            </section>

            <!-- 练习活动 -->
            <section id="practice" class="content-section">
                <h2>练习活动</h2>
                <div class="practice-container">
                    <div class="practice-intro">
                        <p>通过以下练习活动，加深对SCORM 2004概念的理解：</p>
                    </div>
                    
                    <div class="practice-activity">
                        <h3>活动1：概念匹配</h3>
                        <p>将左侧的概念与右侧的定义进行匹配：</p>
                        <div class="matching-game" id="matchingGame">
                            <div class="matching-left">
                                <div class="match-item" data-id="scorm" draggable="true">SCORM</div>
                                <div class="match-item" data-id="sco" draggable="true">SCO</div>
                                <div class="match-item" data-id="lms" draggable="true">LMS</div>
                                <div class="match-item" data-id="api" draggable="true">API</div>
                            </div>
                            <div class="matching-right">
                                <div class="match-target" data-target="scorm">电子学习标准规范</div>
                                <div class="match-target" data-target="sco">可共享内容对象</div>
                                <div class="match-target" data-target="lms">学习管理系统</div>
                                <div class="match-target" data-target="api">应用程序接口</div>
                            </div>
                        </div>
                        <div class="activity-result" id="matchingResult" style="display: none;">
                            <p class="result-text"></p>
                        </div>
                    </div>
                    
                    <div class="practice-activity">
                        <h3>活动2：排序练习</h3>
                        <p>将SCORM内容开发的步骤按正确顺序排列：</p>
                        <div class="sorting-game" id="sortingGame">
                            <div class="sort-item" data-order="3">打包内容</div>
                            <div class="sort-item" data-order="1">分析需求</div>
                            <div class="sort-item" data-order="4">测试部署</div>
                            <div class="sort-item" data-order="2">开发内容</div>
                        </div>
                        <button class="btn btn-info" onclick="checkSorting()">检查排序</button>
                        <div class="activity-result" id="sortingResult" style="display: none;">
                            <p class="result-text"></p>
                        </div>
                    </div>
                </div>
                <div class="section-actions">
                    <button class="btn btn-secondary" onclick="prevSection()">上一页</button>
                    <button class="btn btn-primary" onclick="nextSection()">下一页</button>
                </div>
            </section>

            <!-- 知识检测 -->
            <section id="assessment" class="content-section">
                <h2>知识检测</h2>
                <div class="assessment-container">
                    <div class="assessment-info">
                        <div class="info-item">
                            <span class="info-label">题目数量：</span>
                            <span class="info-value">5题</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">及格分数：</span>
                            <span class="info-value">75分</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">时间限制：</span>
                            <span class="info-value">10分钟</span>
                        </div>
                    </div>
                    
                    <div class="quiz-container" id="quizContainer">
                        <!-- 题目将通过JavaScript动态生成 -->
                    </div>
                    
                    <div class="quiz-actions">
                        <button class="btn btn-success" onclick="submitAssessment()" id="submitBtn">提交答案</button>
                        <button class="btn btn-primary" onclick="completeUnit()" id="completeBtn" style="display: none;">完成单元</button>
                    </div>
                    
                    <div class="assessment-result" id="assessmentResult" style="display: none;">
                        <h3>测试结果</h3>
                        <div class="result-summary">
                            <div class="result-item">
                                <span class="result-label">得分：</span>
                                <span class="result-value" id="finalScore">0</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">正确率：</span>
                                <span class="result-value" id="accuracy">0%</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">状态：</span>
                                <span class="result-value" id="passStatus">未通过</span>
                            </div>
                        </div>
                        <div class="result-feedback" id="resultFeedback">
                            <!-- 反馈内容将通过JavaScript生成 -->
                        </div>
                    </div>
                </div>
                <div class="section-actions">
                    <button class="btn btn-secondary" onclick="prevSection()">上一页</button>
                </div>
            </section>
        </main>

        <footer class="unit-footer">
            <div class="scorm-status">
                <div class="status-item">
                    <span class="status-label">学习者：</span>
                    <span class="status-value" id="learnerName">加载中...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">完成状态：</span>
                    <span class="status-value" id="completionStatus">未开始</span>
                </div>
                <div class="status-item">
                    <span class="status-label">成功状态：</span>
                    <span class="status-value" id="successStatus">未知</span>
                </div>
                <div class="status-item">
                    <span class="status-label">学习时间：</span>
                    <span class="status-value" id="sessionTime">00:00:00</span>
                </div>
            </div>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
