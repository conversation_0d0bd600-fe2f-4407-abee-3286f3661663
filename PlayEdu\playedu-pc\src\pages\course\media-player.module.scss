.media-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  z-index: 9999;
  overflow: hidden;

  .top-cont {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.8) 0%,
      rgba(0, 0, 0, 0) 100%
    );
    z-index: 10;

    .box {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 20px;

      .close-btn {
        display: flex;
        align-items: center;
        color: #fff;
        cursor: pointer;
        font-size: 16px;
        transition: all 0.3s;

        &:hover {
          color: #1890ff;
        }

        span {
          margin-left: 8px;
        }
      }
    }
  }

  .media-body {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px 20px;

    .media-title {
      color: #fff;
      font-size: 24px;
      font-weight: 500;
      margin-bottom: 20px;
      text-align: center;
      max-width: 80%;
      line-height: 1.4;
    }

    .media-box {
      position: relative;
      width: 100%;
      max-width: 1200px;
      height: calc(100vh - 160px);
      background: #000;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

      .loading-container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);

        :global(.ant-spin-text) {
          color: #fff;
        }
      }

      .unsupported-media {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: #1a1a1a;
        color: #fff;
        font-size: 18px;
      }

      .alert-message {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        border-radius: 8px;
        padding: 20px;
        z-index: 20;

        .des-media {
          color: #fff;
          font-size: 16px;
          text-align: center;
          margin-bottom: 16px;
        }

        .alert-button {
          background: #1890ff;
          color: #fff;
          border: none;
          border-radius: 6px;
          padding: 12px 24px;
          font-size: 16px;
          cursor: pointer;
          transition: all 0.3s;
          text-align: center;

          &:hover {
            background: #40a9ff;
          }
        }
      }

      // 确保播放器组件填满容器
      :global(.audio-player),
      :global(.document-viewer),
      :global(.live-player) {
        width: 100%;
        height: 100%;
        border-radius: 0;
        box-shadow: none;
        border: none;
      }

      // 视频播放器样式
      .play-box {
        width: 100%;
        height: 100%;
        background: #000;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .media-mask {
    .media-body {
      padding: 70px 15px 15px;

      .media-title {
        font-size: 20px;
        margin-bottom: 15px;
      }

      .media-box {
        height: calc(100vh - 140px);
      }
    }
  }
}

@media (max-width: 768px) {
  .media-mask {
    .top-cont {
      height: 50px;

      .box {
        padding: 0 15px;

        .close-btn {
          font-size: 14px;
        }
      }
    }

    .media-body {
      padding: 60px 10px 10px;

      .media-title {
        font-size: 18px;
        margin-bottom: 12px;
        max-width: 90%;
      }

      .media-box {
        height: calc(100vh - 120px);
        border-radius: 4px;

        .alert-message {
          padding: 15px;

          .des-media {
            font-size: 14px;
            margin-bottom: 12px;
          }

          .alert-button {
            padding: 10px 20px;
            font-size: 14px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .media-mask {
    .media-body {
      padding: 50px 8px 8px;

      .media-title {
        font-size: 16px;
        margin-bottom: 10px;
      }

      .media-box {
        height: calc(100vh - 100px);

        .alert-message {
          padding: 12px;

          .des-media {
            font-size: 13px;
            margin-bottom: 10px;
          }

          .alert-button {
            padding: 8px 16px;
            font-size: 13px;
          }
        }
      }
    }
  }
}

// 全屏模式样式
@media (orientation: landscape) and (max-height: 600px) {
  .media-mask {
    .media-body {
      padding: 40px 10px 10px;

      .media-title {
        font-size: 16px;
        margin-bottom: 8px;
      }

      .media-box {
        height: calc(100vh - 80px);
      }
    }
  }
}
