/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.course.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import xyz.playedu.course.domain.ScormAttempt;
import xyz.playedu.course.domain.ScormPackage;
import xyz.playedu.course.mapper.ScormAttemptMapper;
import xyz.playedu.course.service.ScormAttemptService;
import xyz.playedu.course.service.ScormPackageService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * SCORM学习尝试服务实现类
 *
 * <AUTHOR> Team
 */
@Service
@Slf4j
public class ScormAttemptServiceImpl extends ServiceImpl<ScormAttemptMapper, ScormAttempt> implements ScormAttemptService {

    @Autowired
    private ScormPackageService scormPackageService;

    @Override
    public List<ScormAttempt> getByUserAndPackage(Integer userId, Integer packageId) {
        QueryWrapper<ScormAttempt> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("package_id", packageId);
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public ScormAttempt getCurrentAttempt(Integer userId, Integer packageId) {
        QueryWrapper<ScormAttempt> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("package_id", packageId);
        queryWrapper.orderByDesc("created_at");
        queryWrapper.last("LIMIT 1");
        return getOne(queryWrapper);
    }

    @Override
    public ScormAttempt createAttempt(Integer userId, Integer packageId, Integer hourId) {
        // 获取用户在该包的尝试次数
        QueryWrapper<ScormAttempt> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("package_id", packageId);
        int attemptCount = (int) count(queryWrapper);

        ScormAttempt attempt = new ScormAttempt();
        attempt.setUserId(userId);
        attempt.setPackageId(packageId);
        attempt.setHourId(hourId);
        attempt.setAttemptNumber(attemptCount + 1);
        attempt.setLessonStatus("not attempted");
        attempt.setCompletionStatus("not attempted");
        attempt.setSuccessStatus("unknown");
        attempt.setEntry("ab-initio");
        attempt.setMode("normal");
        attempt.setCredit("credit");
        attempt.setStartTime(new Date());
        attempt.setCreatedAt(new Date());
        attempt.setUpdatedAt(new Date());

        save(attempt);
        return attempt;
    }

    @Override
    public ScormAttempt updateAttempt(Integer id, ScormAttempt attempt) {
        attempt.setId(id);
        attempt.setUpdatedAt(new Date());
        updateById(attempt);
        return getById(id);
    }

    @Override
    public void deleteAttempt(Integer id) {
        removeById(id);
    }

    @Override
    public boolean updateProgress(Integer attemptId, String element, String value) {
        try {
            ScormAttempt attempt = getById(attemptId);
            if (attempt == null) {
                log.error("学习尝试不存在: {}", attemptId);
                return false;
            }

            // 根据SCORM数据元素更新对应字段
            boolean updated = updateScormElement(attempt, element, value);

            if (updated) {
                attempt.setUpdatedAt(new Date());
                updateById(attempt);
                log.debug("更新SCORM数据: attemptId={}, element={}, value={}", attemptId, element, value);
            }

            return updated;
        } catch (Exception e) {
            log.error("更新学习进度失败", e);
            return false;
        }
    }

    /**
     * 根据SCORM数据元素更新对应字段
     */
    private boolean updateScormElement(ScormAttempt attempt, String element, String value) {
        switch (element) {
            case "cmi.core.lesson_status":
            case "cmi.completion_status":
                attempt.setLessonStatus(value);
                attempt.setCompletionStatus(value);
                break;
            case "cmi.core.score.raw":
            case "cmi.score.raw":
                try {
                    attempt.setScoreRaw(Double.parseDouble(value));
                } catch (NumberFormatException e) {
                    return false;
                }
                break;
            case "cmi.core.score.max":
            case "cmi.score.max":
                try {
                    attempt.setScoreMax(Double.parseDouble(value));
                } catch (NumberFormatException e) {
                    return false;
                }
                break;
            case "cmi.core.score.min":
            case "cmi.score.min":
                try {
                    attempt.setScoreMin(Double.parseDouble(value));
                } catch (NumberFormatException e) {
                    return false;
                }
                break;
            case "cmi.progress_measure":
                try {
                    attempt.setProgressMeasure(Double.parseDouble(value));
                } catch (NumberFormatException e) {
                    return false;
                }
                break;
            case "cmi.location":
            case "cmi.core.lesson_location":
                attempt.setLocation(value);
                break;
            case "cmi.suspend_data":
                attempt.setSuspendData(value);
                break;
            case "cmi.exit":
                attempt.setExit(value);
                break;
            case "cmi.success_status":
                attempt.setSuccessStatus(value);
                break;
            default:
                // 其他数据元素暂不处理
                return false;
        }
        return true;
    }

    @Override
    public boolean commitData(Integer attemptId) {
        ScormAttempt attempt = getById(attemptId);
        if (attempt == null) {
            return false;
        }

        attempt.setUpdatedAt(new Date());
        updateById(attempt);
        return true;
    }

    @Override
    public boolean terminateSession(Integer attemptId) {
        ScormAttempt attempt = getById(attemptId);
        if (attempt == null) {
            return false;
        }

        attempt.setEndTime(new Date());
        attempt.setUpdatedAt(new Date());
        updateById(attempt);
        return true;
    }

    @Override
    public List<ScormAttempt> getUserAttempts(Integer userId, Integer packageId) {
        QueryWrapper<ScormAttempt> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        if (packageId != null) {
            queryWrapper.eq("package_id", packageId);
        }
        queryWrapper.orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public Map<String, Object> getPackageStatistics(Integer packageId) {
        Map<String, Object> statistics = new HashMap<>();

        QueryWrapper<ScormAttempt> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("package_id", packageId);

        List<ScormAttempt> attempts = list(queryWrapper);

        long totalAttempts = attempts.size();
        long uniqueUsers = attempts.stream().map(ScormAttempt::getUserId).distinct().count();
        long completedAttempts = attempts.stream().filter(this::isCompleted).count();
        long passedAttempts = attempts.stream().filter(this::isPassed).count();

        Double avgScore = attempts.stream()
            .filter(a -> a.getScoreRaw() != null)
            .mapToDouble(a -> a.getScoreRaw().doubleValue())
            .average()
            .orElse(0.0);

        Double avgProgress = attempts.stream()
            .mapToDouble(this::calculateProgress)
            .average()
            .orElse(0.0);

        statistics.put("total_attempts", totalAttempts);
        statistics.put("unique_users", uniqueUsers);
        statistics.put("completed_attempts", completedAttempts);
        statistics.put("passed_attempts", passedAttempts);
        statistics.put("completion_rate", totalAttempts > 0 ? (double) completedAttempts / totalAttempts * 100 : 0.0);
        statistics.put("pass_rate", totalAttempts > 0 ? (double) passedAttempts / totalAttempts * 100 : 0.0);
        statistics.put("average_score", avgScore);
        statistics.put("average_progress", avgProgress);

        return statistics;
    }

    @Override
    public Map<String, Object> getUserStatistics(Integer userId) {
        Map<String, Object> statistics = new HashMap<>();

        QueryWrapper<ScormAttempt> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);

        List<ScormAttempt> attempts = list(queryWrapper);

        long totalAttempts = attempts.size();
        long completedAttempts = attempts.stream().filter(this::isCompleted).count();
        long passedAttempts = attempts.stream().filter(this::isPassed).count();
        long uniquePackages = attempts.stream().map(ScormAttempt::getPackageId).distinct().count();

        Double avgScore = attempts.stream()
            .filter(a -> a.getScoreRaw() != null)
            .mapToDouble(a -> a.getScoreRaw().doubleValue())
            .average()
            .orElse(0.0);

        Double avgProgress = attempts.stream()
            .mapToDouble(this::calculateProgress)
            .average()
            .orElse(0.0);

        statistics.put("total_attempts", totalAttempts);
        statistics.put("unique_packages", uniquePackages);
        statistics.put("completed_attempts", completedAttempts);
        statistics.put("passed_attempts", passedAttempts);
        statistics.put("completion_rate", totalAttempts > 0 ? (double) completedAttempts / totalAttempts * 100 : 0.0);
        statistics.put("pass_rate", totalAttempts > 0 ? (double) passedAttempts / totalAttempts * 100 : 0.0);
        statistics.put("average_score", avgScore);
        statistics.put("average_progress", avgProgress);

        return statistics;
    }

    @Override
    public Map<String, Object> getLearningReport(Integer packageId, Integer page, Integer size) {
        Map<String, Object> report = new HashMap<>();

        QueryWrapper<ScormAttempt> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("package_id", packageId);
        queryWrapper.orderByDesc("created_at");

        // 分页查询
        int offset = (page - 1) * size;
        queryWrapper.last("LIMIT " + size + " OFFSET " + offset);

        List<ScormAttempt> attempts = list(queryWrapper);

        // 获取总数
        QueryWrapper<ScormAttempt> countWrapper = new QueryWrapper<>();
        countWrapper.eq("package_id", packageId);
        long total = count(countWrapper);

        // 构建报告数据
        List<Map<String, Object>> reportData = attempts.stream().map(attempt -> {
            Map<String, Object> data = new HashMap<>();
            data.put("attempt_id", attempt.getId());
            data.put("user_id", attempt.getUserId());
            data.put("attempt_number", attempt.getAttemptNumber());
            data.put("lesson_status", attempt.getLessonStatus());
            data.put("completion_status", attempt.getCompletionStatus());
            data.put("success_status", attempt.getSuccessStatus());
            data.put("score_raw", attempt.getScoreRaw());
            data.put("score_max", attempt.getScoreMax());
            data.put("score_min", attempt.getScoreMin());
            data.put("progress", calculateProgress(attempt));
            data.put("start_time", attempt.getStartTime());
            data.put("end_time", attempt.getEndTime());
            data.put("total_time", attempt.getTotalTime());
            data.put("is_completed", isCompleted(attempt));
            data.put("is_passed", isPassed(attempt));
            return data;
        }).collect(Collectors.toList());

        report.put("data", reportData);
        report.put("total", total);
        report.put("page", page);
        report.put("size", size);
        report.put("pages", (total + size - 1) / size);

        return report;
    }

    @Override
    public List<Map<String, Object>> exportLearningData(Integer packageId) {
        QueryWrapper<ScormAttempt> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("package_id", packageId);
        queryWrapper.orderByDesc("created_at");

        List<ScormAttempt> attempts = list(queryWrapper);

        return attempts.stream().map(attempt -> {
            Map<String, Object> data = new HashMap<>();
            data.put("用户ID", attempt.getUserId());
            data.put("尝试次数", attempt.getAttemptNumber());
            data.put("课程状态", attempt.getLessonStatus());
            data.put("完成状态", attempt.getCompletionStatus());
            data.put("成功状态", attempt.getSuccessStatus());
            data.put("原始分数", attempt.getScoreRaw());
            data.put("最高分数", attempt.getScoreMax());
            data.put("最低分数", attempt.getScoreMin());
            data.put("进度百分比", calculateProgress(attempt));
            data.put("开始时间", attempt.getStartTime());
            data.put("结束时间", attempt.getEndTime());
            data.put("总时间", attempt.getTotalTime());
            data.put("是否完成", isCompleted(attempt) ? "是" : "否");
            data.put("是否通过", isPassed(attempt) ? "是" : "否");
            data.put("创建时间", attempt.getCreatedAt());
            data.put("更新时间", attempt.getUpdatedAt());
            return data;
        }).collect(Collectors.toList());
    }

    @Override
    public Double calculateProgress(ScormAttempt attempt) {
        if (attempt == null) {
            return 0.0;
        }

        // 如果有进度测量值，直接使用
        if (attempt.getProgressMeasure() != null) {
            return attempt.getProgressMeasure().doubleValue() * 100;
        }

        // 根据课程状态计算进度
        String lessonStatus = attempt.getLessonStatus();
        if (lessonStatus != null) {
            switch (lessonStatus.toLowerCase()) {
                case "completed":
                case "passed":
                    return 100.0;
                case "incomplete":
                case "failed":
                    return 50.0;
                case "browsed":
                    return 25.0;
                case "not attempted":
                default:
                    return 0.0;
            }
        }

        return 0.0;
    }

    @Override
    public boolean isCompleted(ScormAttempt attempt) {
        if (attempt == null) {
            return false;
        }

        String lessonStatus = attempt.getLessonStatus();
        String completionStatus = attempt.getCompletionStatus();

        return "completed".equalsIgnoreCase(lessonStatus) ||
               "passed".equalsIgnoreCase(lessonStatus) ||
               "completed".equalsIgnoreCase(completionStatus);
    }

    @Override
    public boolean isPassed(ScormAttempt attempt) {
        if (attempt == null) {
            return false;
        }

        String lessonStatus = attempt.getLessonStatus();
        String successStatus = attempt.getSuccessStatus();

        return "passed".equalsIgnoreCase(lessonStatus) ||
               "passed".equalsIgnoreCase(successStatus);
    }
}
