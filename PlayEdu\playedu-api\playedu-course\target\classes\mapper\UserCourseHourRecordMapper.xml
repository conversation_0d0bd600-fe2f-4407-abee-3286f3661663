<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.course.mapper.UserCourseHourRecordMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.course.domain.UserCourseHourRecord">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="courseId" column="course_id" jdbcType="INTEGER"/>
        <result property="hourId" column="hour_id" jdbcType="INTEGER"/>
        <result property="totalDuration" column="total_duration" jdbcType="INTEGER"/>
        <result property="finishedDuration" column="finished_duration" jdbcType="INTEGER"/>
        <result property="realDuration" column="real_duration" jdbcType="INTEGER"/>
        <result property="isFinished" column="is_finished" jdbcType="TINYINT"/>
        <result property="finishedAt" column="finished_at" jdbcType="TIMESTAMP"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,course_id,
        hour_id,total_duration,finished_duration,
        real_duration,is_finished,finished_at,
        created_at,updated_at
    </sql>

    <select id="getUserLatestRecords" resultType="xyz.playedu.course.domain.UserCourseHourRecord">
        select `t1`.*
        from `user_course_hour_records` as `t1`
                 inner join (select `course_id`, max(`updated_at`) as `latest_at`
                             from `user_course_hour_records`
                             where `user_id` = #{userId}
                             group by `course_id`) as `t2`
                            on `t2`.`course_id` = `t1`.`course_id` and
                               `t2`.`latest_at` = `t1`.`updated_at`
        order by `t1`.`updated_at` desc
            limit #{size};
    </select>
    <select id="getUserCourseHourCount"
            resultType="xyz.playedu.common.types.mapper.UserCourseHourRecordCourseCountMapper">
        SELECT `course_id`, count(1) AS `total`
        FROM `user_course_hour_records`
        WHERE `user_id` = #{userId}
        <if test="courseIds != null and courseIds.size > 0">
            AND `course_id` IN (<foreach collection="courseIds" item="courseId" separator=",">#{courseId}</foreach>)
        </if>
        <if test="isFinished != null">
            AND `is_finisehd` = #{isFinished}
        </if>
        GROUP BY `course_id`;
    </select>

    <select id="paginate" resultType="xyz.playedu.course.domain.UserCourseHourRecord">
        SELECT *
        FROM `user_course_hour_records` as `t`
        <where>
            <if test="userId != null">
                AND `t`.`user_id` = #{userId}
            </if>
            <if test="isFinished != null">
                AND `t`.`is_finisehd` = #{isFinished}
            </if>
        </where>
        <if test="sortAlgo == 'asc'">
            <choose>
                <when test="sortField == 'finished_at'">
                    ORDER BY `t`.`finished_at` ASC
                </when>
                <when test="sortField == 'created_at'">
                    ORDER BY `t`.`created_at` ASC
                </when>
                <when test="sortField == 'updated_at'">
                    ORDER BY `t`.`updated_at` ASC
                </when>
                <when test="sortField == 'real_duration'">
                    ORDER BY `t`.`real_duration` ASC
                </when>
                <otherwise>
                    ORDER BY `t`.`id` ASC
                </otherwise>
            </choose>
        </if>
        <if test="sortAlgo != 'asc'">
            <choose>
                <when test="sortField == 'finished_at'">
                    ORDER BY `t`.`finished_at` DESC
                </when>
                <when test="sortField == 'created_at'">
                    ORDER BY `t`.`created_at` DESC
                </when>
                <when test="sortField == 'updated_at'">
                    ORDER BY `t`.`updated_at` DESC
                </when>
                <when test="sortField == 'real_duration'">
                    ORDER BY `t`.`real_duration` DESC
                </when>
                <otherwise>
                    ORDER BY `t`.`id` DESC
                </otherwise>
            </choose>
        </if>
        LIMIT #{pageStart}, #{pageSize};
    </select>

    <select id="paginateCount" resultType="java.lang.Long">
        SELECT count(1)
        FROM `user_course_hour_records` as `t`
        <where>
            <if test="userId != null">
                AND `t`.`user_id` = #{userId}
            </if>
            <if test="isFinished != null">
                AND `t`.`is_finisehd` = #{isFinished}
            </if>
        </where>
    </select>
    <select id="getUserCourseHourUserCount"
            resultType="xyz.playedu.common.types.mapper.UserCourseHourRecordUserCountMapper">
        SELECT `user_id`, count(1) AS `total`
        FROM `user_course_hour_records`
        WHERE `course_id` = #{courseId}
        <if test="userIds != null and userIds.size > 0">
            AND `user_id` IN (<foreach collection="userIds" item="userId" separator=",">#{userId}</foreach>)
        </if>
        <if test="isFinished != null">
            AND `is_finisehd` = #{isFinished}
        </if>
        GROUP BY `user_id`;
    </select>

    <select id="getUserCourseHourUserFirstCreatedAt"
            resultType="xyz.playedu.common.types.mapper.UserCourseHourRecordUserFirstCreatedAtMapper">
        SELECT `t1`.`created_at`, `t1`.`user_id`
        FROM `user_course_hour_records` AS `t1`
        INNER JOIN (SELECT `t2`.`user_id`, min(`t2`.`created_at`) as `created_at`
        FROM `user_course_hour_records` AS `t2`
        WHERE `t2`.`course_id` = #{courseId}
        <if test="userIds != null and userIds.size > 0">
            AND `t2`.`user_id` IN (<foreach collection="userIds" item="userId" separator=",">#{userId}</foreach>)
        </if>
        GROUP BY `t2`.`user_id`) AS `t3`
        ON `t3`.`user_id` = `t1`.`user_id`
        AND `t3`.`created_at` = `t1`.`created_at`
        WHERE `t1`.`course_id` = #{courseId}
        <if test="userIds != null and userIds.size > 0">
            AND `t1`.`user_id` IN (<foreach collection="userIds" item="userId" separator=",">#{userId}</foreach>)
        </if>
    </select>

    <select id="getUserPerCourseEarliestRecord" resultType="xyz.playedu.course.domain.UserCourseHourRecord">
        SELECT
            `a`.*
        FROM
            `user_course_hour_records` AS `a`
                INNER JOIN (
                SELECT
                    min(`created_at`) AS `min_created_at`,
                    `course_id`,
                    `user_id`
                FROM
                    `user_course_hour_records`
                WHERE
                    `user_id` = #{userId}
                GROUP BY
                    `user_id`,
                    `course_id`) AS `b` ON `b`.`min_created_at` = `a`.`created_at`
                AND `b`.`course_id` = `a`.`course_id`
                AND `b`.`user_id` = `a`.`user_id`;
    </select>

    <select id="getCoursePerUserEarliestRecord" resultType="xyz.playedu.course.domain.UserCourseHourRecord">
        SELECT
            `a`.*
        FROM
            `user_course_hour_records` AS `a`
                INNER JOIN (
                SELECT
                    min(`created_at`) AS `min_created_at`,
                    `course_id`,
                    `user_id`
                FROM
                    `user_course_hour_records`
                WHERE
                    `course_id` = #{courseId}
                GROUP BY
                    `course_id`,
                    `user_id`) AS `b` ON `b`.`min_created_at` = `a`.`created_at`
                AND `b`.`course_id` = `a`.`course_id`
                AND `b`.`user_id` = `a`.`user_id`;
    </select>
</mapper>
