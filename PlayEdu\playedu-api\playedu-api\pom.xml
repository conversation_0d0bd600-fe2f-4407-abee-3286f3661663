<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>xyz.playedu</groupId>
        <artifactId>playedu</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>playedu-api</artifactId>
    <version>2.0</version>

    <dependencies>
        <dependency>
            <groupId>xyz.playedu</groupId>
            <artifactId>playedu-common</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>xyz.playedu</groupId>
            <artifactId>playedu-system</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>xyz.playedu</groupId>
            <artifactId>playedu-course</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>xyz.playedu</groupId>
            <artifactId>playedu-resource</artifactId>
            <version>1.0</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>playedu-api</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.3.4</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>