/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.controller.backend;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xyz.playedu.common.annotation.BackendPermission;
import xyz.playedu.common.annotation.Log;
import xyz.playedu.common.constant.BPermissionConstant;
import xyz.playedu.common.constant.BusinessTypeConstant;
import xyz.playedu.common.exception.ServiceException;
import xyz.playedu.common.types.JsonResponse;
import xyz.playedu.common.types.paginate.PaginationResult;
import xyz.playedu.course.domain.ScormAttempt;
import xyz.playedu.course.domain.ScormPackage;
import xyz.playedu.course.service.ScormAttemptService;
import xyz.playedu.course.service.ScormPackageService;
import xyz.playedu.course.service.ScormParseService;
import xyz.playedu.resource.domain.Resource;
import xyz.playedu.resource.service.ResourceService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import jakarta.servlet.http.HttpServletRequest;

import java.util.HashMap;
import java.util.Map;

/**
 * SCORM后端管理控制器
 *
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/backend/v1/scorm")
@Slf4j
public class ScormController {

    @Autowired
    private ScormPackageService scormPackageService;

    @Autowired
    private ScormAttemptService scormAttemptService;

    @Autowired
    private ScormParseService scormParseService;

    @Autowired
    private ResourceService resourceService;

    // @BackendPermission(slug = BPermissionConstant.SCORM_PACKAGE)
    @GetMapping("/packages")
    public JsonResponse packageList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String title) {

        QueryWrapper<ScormPackage> queryWrapper = new QueryWrapper<>();
        if (title != null && !title.trim().isEmpty()) {
            queryWrapper.like("title", title);
        }
        queryWrapper.orderByDesc("created_at");

        IPage<ScormPackage> pageResult = scormPackageService.page(new Page<>(page, size), queryWrapper);

        PaginationResult<ScormPackage> result = new PaginationResult<>();
        result.setTotal(pageResult.getTotal());
        result.setData(pageResult.getRecords());

        return JsonResponse.data(result);
    }

    // @BackendPermission(slug = BPermissionConstant.SCORM_PACKAGE)
    @GetMapping("/packages/statistics")
    public JsonResponse packageStatistics() {
        QueryWrapper<ScormPackage> queryWrapper = new QueryWrapper<>();
        
        long total = scormPackageService.count();
        
        queryWrapper.clear();
        queryWrapper.eq("parse_status", 1);
        long parsed = scormPackageService.count(queryWrapper);
        
        queryWrapper.clear();
        queryWrapper.eq("parse_status", 0);
        long parsing = scormPackageService.count(queryWrapper);
        
        queryWrapper.clear();
        queryWrapper.eq("parse_status", 2);
        long failed = scormPackageService.count(queryWrapper);

        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("parsed", parsed);
        data.put("parsing", parsing);
        data.put("failed", failed);

        return JsonResponse.data(data);
    }

    // @BackendPermission(slug = BPermissionConstant.SCORM_PACKAGE)
    @GetMapping("/packages/{id}")
    public JsonResponse packageDetail(@PathVariable Integer id) throws ServiceException {
        ScormPackage scormPackage = scormPackageService.getById(id);
        if (scormPackage == null) {
            throw new ServiceException("SCORM包不存在");
        }
        return JsonResponse.data(scormPackage);
    }

    @BackendPermission(slug = BPermissionConstant.SCORM_PACKAGE_CUD)
    @Log(title = "删除SCORM包", businessType = BusinessTypeConstant.DELETE)
    @DeleteMapping("/packages/{id}")
    public JsonResponse destroyPackage(@PathVariable Integer id) throws ServiceException {
        ScormPackage scormPackage = scormPackageService.getById(id);
        if (scormPackage == null) {
            throw new ServiceException("SCORM包不存在");
        }

        // 获取关联的资源
        Resource resource = resourceService.getById(scormPackage.getResourceId());

        // 删除SCORM包记录
        scormPackageService.removeById(id);

        // 清理解压后的文件
        if (scormPackage.getExtractPath() != null && !scormPackage.getExtractPath().isEmpty()) {
            scormParseService.cleanupFailedParse(scormPackage.getExtractPath());
        }

        // 删除关联的学习尝试记录
        scormAttemptService.remove(
            new QueryWrapper<ScormAttempt>().eq("package_id", id)
        );

        return JsonResponse.success("SCORM包删除成功");
    }

    @BackendPermission(slug = BPermissionConstant.SCORM_PACKAGE_CUD)
    @Log(title = "重新解析SCORM包", businessType = BusinessTypeConstant.UPDATE)
    @PostMapping("/packages/{id}/reparse")
    public JsonResponse reparsePackage(@PathVariable Integer id) throws ServiceException {
        ScormPackage scormPackage = scormPackageService.getById(id);
        if (scormPackage == null) {
            throw new ServiceException("SCORM包不存在");
        }

        // 获取关联的资源
        Resource resource = resourceService.getById(scormPackage.getResourceId());
        if (resource == null) {
            throw new ServiceException("关联的资源不存在");
        }

        // 重置解析状态
        scormPackage.setParseStatus(0);
        scormPackage.setParseError(null);
        scormPackageService.updateById(scormPackage);

        // 异步重新解析
        scormParseService.parseScormPackageAsync(scormPackage, resource);

        return JsonResponse.success("重新解析任务已启动");
    }

    @BackendPermission(slug = BPermissionConstant.SCORM_ATTEMPT)
    @GetMapping("/attempts")
    public JsonResponse attemptList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer packageId,
            @RequestParam(required = false) Integer userId) {

        QueryWrapper<ScormAttempt> queryWrapper = new QueryWrapper<>();
        if (packageId != null) {
            queryWrapper.eq("package_id", packageId);
        }
        if (userId != null) {
            queryWrapper.eq("user_id", userId);
        }
        queryWrapper.orderByDesc("created_at");

        IPage<ScormAttempt> pageResult = scormAttemptService.page(new Page<>(page, size), queryWrapper);

        PaginationResult<ScormAttempt> result = new PaginationResult<>();
        result.setTotal(pageResult.getTotal());
        result.setData(pageResult.getRecords());

        return JsonResponse.data(result);
    }

    @BackendPermission(slug = BPermissionConstant.SCORM_ATTEMPT)
    @GetMapping("/attempts/{id}")
    public JsonResponse attemptDetail(@PathVariable Integer id) throws ServiceException {
        ScormAttempt attempt = scormAttemptService.getById(id);
        if (attempt == null) {
            throw new ServiceException("学习记录不存在");
        }
        return JsonResponse.data(attempt);
    }

    @BackendPermission(slug = BPermissionConstant.SCORM_PACKAGE)
    @GetMapping("/packages/{id}/report")
    public JsonResponse learningReport(@PathVariable Integer id) throws ServiceException {
        ScormPackage scormPackage = scormPackageService.getById(id);
        if (scormPackage == null) {
            throw new ServiceException("SCORM包不存在");
        }

        // 获取学习统计数据
        QueryWrapper<ScormAttempt> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("package_id", id);
        
        long totalAttempts = scormAttemptService.count(queryWrapper);
        
        queryWrapper.clear();
        queryWrapper.eq("package_id", id);
        queryWrapper.eq("completion_status", "completed");
        long completedAttempts = scormAttemptService.count(queryWrapper);
        
        queryWrapper.clear();
        queryWrapper.eq("package_id", id);
        queryWrapper.eq("success_status", "passed");
        long passedAttempts = scormAttemptService.count(queryWrapper);

        Map<String, Object> report = new HashMap<>();
        report.put("packageInfo", scormPackage);
        report.put("totalAttempts", totalAttempts);
        report.put("completedAttempts", completedAttempts);
        report.put("passedAttempts", passedAttempts);
        report.put("completionRate", totalAttempts > 0 ? (double) completedAttempts / totalAttempts : 0);
        report.put("passRate", totalAttempts > 0 ? (double) passedAttempts / totalAttempts : 0);

        return JsonResponse.data(report);
    }

    @BackendPermission(slug = BPermissionConstant.SCORM_PACKAGE)
    @GetMapping("/packages/{id}/export")
    public JsonResponse exportLearningData(@PathVariable Integer id) throws ServiceException {
        ScormPackage scormPackage = scormPackageService.getById(id);
        if (scormPackage == null) {
            throw new ServiceException("SCORM包不存在");
        }

        // TODO: 实现学习数据导出逻辑
        return JsonResponse.success("导出功能开发中");
    }

    /**
     * 访问SCORM包中的文件
     */
    @GetMapping("/packages/{id}/files/**")
    public ResponseEntity<ByteArrayResource> getScormFile(
            @PathVariable Integer id,
            HttpServletRequest request) throws ServiceException {

        // 获取SCORM包信息
        ScormPackage scormPackage = scormPackageService.getById(id);
        if (scormPackage == null) {
            throw new ServiceException("SCORM包不存在");
        }

        if (scormPackage.getParseStatus() != 1) {
            throw new ServiceException("SCORM包尚未解析完成");
        }

        // 获取请求的文件路径
        String requestURI = request.getRequestURI();
        String filePath = requestURI.substring(requestURI.indexOf("/files/") + 7);

        try {
            // 构建完整的文件路径
            String extractPath = scormPackage.getExtractPath();
            Path fullPath = Paths.get(extractPath, filePath);

            // 检查文件是否存在
            if (!Files.exists(fullPath)) {
                throw new ServiceException("文件不存在: " + filePath);
            }

            // 读取文件内容
            byte[] fileContent = Files.readAllBytes(fullPath);

            // 确定文件类型
            String fileName = fullPath.getFileName().toString();
            String contentType = getContentType(fileName);

            // 返回文件内容
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + fileName + "\"")
                    .body(new ByteArrayResource(fileContent));

        } catch (Exception e) {
            log.error("读取SCORM文件失败: {}", filePath, e);
            throw new ServiceException("读取文件失败: " + e.getMessage());
        }
    }

    /**
     * 根据文件扩展名确定Content-Type
     */
    private String getContentType(String fileName) {
        String extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
        switch (extension) {
            case "html":
            case "htm":
                return "text/html";
            case "css":
                return "text/css";
            case "js":
                return "application/javascript";
            case "json":
                return "application/json";
            case "xml":
                return "application/xml";
            case "png":
                return "image/png";
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "gif":
                return "image/gif";
            case "svg":
                return "image/svg+xml";
            case "pdf":
                return "application/pdf";
            case "mp4":
                return "video/mp4";
            case "mp3":
                return "audio/mpeg";
            default:
                return "application/octet-stream";
        }
    }
}
