.scorm-page {
  .statistics-cards {
    margin-bottom: 24px;
    
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      
      .ant-statistic-title {
        color: #666;
        font-size: 14px;
      }
      
      .ant-statistic-content {
        font-size: 24px;
        font-weight: 600;
      }
    }
  }
  
  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .left-actions {
      .ant-btn {
        margin-right: 8px;
      }
    }
    
    .right-filters {
      .ant-select,
      .ant-input-search {
        margin-left: 8px;
      }
    }
  }
  
  .scorm-table {
    .ant-table {
      border-radius: 8px;
      overflow: hidden;
      
      .ant-table-thead > tr > th {
        background: #fafafa;
        border-bottom: 1px solid #f0f0f0;
        font-weight: 600;
      }
      
      .ant-table-tbody > tr > td {
        border-bottom: 1px solid #f0f0f0;
      }
      
      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }
    
    .status-badge {
      .ant-badge-status-dot {
        width: 8px;
        height: 8px;
      }
    }
    
    .package-info {
      .package-title {
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .package-meta {
        font-size: 12px;
        color: #666;
        
        > span {
          margin-right: 12px;
          
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
    
    .parse-status {
      .status-text {
        font-size: 12px;
        
        &.success {
          color: #52c41a;
        }
        
        &.processing {
          color: #1890ff;
        }
        
        &.error {
          color: #ff4d4f;
        }
      }
    }
    
    .action-buttons {
      .ant-btn {
        margin-right: 4px;
        
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.scorm-detail-modal {
  .detail-section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #262626;
    }
    
    .detail-item {
      display: flex;
      margin-bottom: 8px;
      
      .item-label {
        width: 120px;
        color: #666;
        font-size: 14px;
      }
      
      .item-value {
        flex: 1;
        color: #262626;
        font-size: 14px;
      }
    }
  }
  
  .manifest-content {
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 12px;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    white-space: pre-wrap;
  }
  
  .organization-tree {
    .ant-tree {
      background: #fafafa;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      padding: 8px;
    }
  }
}

.upload-scorm-area {
  .upload-tips {
    margin-top: 16px;
    padding: 12px;
    background: #f0f8ff;
    border: 1px solid #d4edda;
    border-radius: 6px;
    
    .tips-title {
      font-weight: 500;
      margin-bottom: 8px;
      color: #155724;
    }
    
    .tips-list {
      margin: 0;
      padding-left: 20px;
      color: #155724;
      
      li {
        margin-bottom: 4px;
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 768px) {
  .scorm-page {
    .operation-bar {
      flex-direction: column;
      align-items: stretch;
      
      .left-actions {
        margin-bottom: 16px;
      }
      
      .right-filters {
        .ant-select,
        .ant-input-search {
          margin-left: 0;
          margin-top: 8px;
          width: 100%;
        }
      }
    }
    
    .statistics-cards {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }
  
  .scorm-detail-modal {
    .detail-item {
      flex-direction: column;
      
      .item-label {
        width: auto;
        margin-bottom: 4px;
        font-weight: 500;
      }
    }
  }
}
