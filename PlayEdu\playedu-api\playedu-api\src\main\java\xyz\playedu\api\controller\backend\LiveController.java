/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.controller.backend;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xyz.playedu.common.annotation.BackendPermission;
import xyz.playedu.common.annotation.Log;
import xyz.playedu.common.constant.BPermissionConstant;
import xyz.playedu.common.constant.BusinessTypeConstant;
import xyz.playedu.common.context.BCtx;
import xyz.playedu.common.exception.ServiceException;
import xyz.playedu.common.types.JsonResponse;
import xyz.playedu.common.types.paginate.PaginationResult;
import xyz.playedu.course.domain.LiveSession;
import xyz.playedu.course.request.LiveSessionRequest;
import xyz.playedu.course.service.LiveSessionService;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 直播管理控制器
 *
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/backend/v1/live")
@Slf4j
public class LiveController {

    @Autowired
    private LiveSessionService liveSessionService;

    @GetMapping("/sessions")
    // @BackendPermission(slug = BPermissionConstant.LIVE_SESSION)
    public JsonResponse sessionList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(defaultValue = "") String title,
            @RequestParam(required = false) Integer status) {

        PaginationResult<LiveSession> result = liveSessionService.paginate(page, size, title, status);

        HashMap<String, Object> data = new HashMap<>();
        data.put("data", result.getData());
        data.put("total", result.getTotal());
        data.put("current_page", page);
        data.put("per_page", size);

        return JsonResponse.data(data);
    }

    @GetMapping("/sessions/statistics")
    @BackendPermission(slug = BPermissionConstant.LIVE_SESSION)
    public JsonResponse sessionStatistics() {
        List<LiveSession> allSessions = liveSessionService.list();
        
        long total = allSessions.size();
        long living = allSessions.stream().filter(s -> s.getStatus() == 1).count();
        long upcoming = allSessions.stream().filter(s -> s.getStatus() == 0).count();
        long ended = allSessions.stream().filter(s -> s.getStatus() == 2).count();

        HashMap<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("living", living);
        data.put("upcoming", upcoming);
        data.put("ended", ended);

        return JsonResponse.data(data);
    }

    @PostMapping("/sessions")
    @BackendPermission(slug = BPermissionConstant.LIVE_SESSION)
    @Log(title = "创建直播", businessType = BusinessTypeConstant.INSERT)
    public JsonResponse createSession(@RequestBody @Validated LiveSessionRequest request) {
        LiveSession session = liveSessionService.create(
                request.getTitle(),
                request.getDescription(),
                request.getThumb(),
                request.getStartTime(),
                request.getEndTime(),
                request.getMaxViewers(),
                request.getIsRecord(),
                BCtx.getId()
        );

        return JsonResponse.data(session);
    }

    @GetMapping("/sessions/{id}")
    @BackendPermission(slug = BPermissionConstant.LIVE_SESSION)
    public JsonResponse sessionDetail(@PathVariable Integer id) throws ServiceException {
        LiveSession session = liveSessionService.getById(id);
        if (session == null) {
            throw new ServiceException("直播场次不存在");
        }

        return JsonResponse.data(session);
    }

    @PutMapping("/sessions/{id}")
    @BackendPermission(slug = BPermissionConstant.LIVE_SESSION)
    @Log(title = "更新直播", businessType = BusinessTypeConstant.UPDATE)
    public JsonResponse updateSession(
            @PathVariable Integer id,
            @RequestBody @Validated LiveSessionRequest request) throws ServiceException {
        
        LiveSession session = liveSessionService.getById(id);
        if (session == null) {
            throw new ServiceException("直播场次不存在");
        }

        liveSessionService.update(
                session,
                request.getTitle(),
                request.getDescription(),
                request.getThumb(),
                request.getStartTime(),
                request.getEndTime(),
                request.getMaxViewers(),
                request.getIsRecord()
        );

        return JsonResponse.success();
    }

    @DeleteMapping("/sessions/{id}")
    @BackendPermission(slug = BPermissionConstant.LIVE_SESSION)
    @Log(title = "删除直播", businessType = BusinessTypeConstant.DELETE)
    public JsonResponse destroySession(@PathVariable Integer id) throws ServiceException {
        LiveSession session = liveSessionService.getById(id);
        if (session == null) {
            throw new ServiceException("直播场次不存在");
        }

        liveSessionService.removeById(id);
        return JsonResponse.success();
    }

    @PostMapping("/sessions/{id}/start")
    @BackendPermission(slug = BPermissionConstant.LIVE_SESSION)
    @Log(title = "开始直播", businessType = BusinessTypeConstant.UPDATE)
    public JsonResponse startSession(@PathVariable Integer id) throws ServiceException {
        String pushUrl = liveSessionService.startLive(id);
        
        HashMap<String, Object> data = new HashMap<>();
        data.put("push_url", pushUrl);
        
        return JsonResponse.data(data);
    }

    @PostMapping("/sessions/{id}/end")
    @BackendPermission(slug = BPermissionConstant.LIVE_SESSION)
    @Log(title = "结束直播", businessType = BusinessTypeConstant.UPDATE)
    public JsonResponse endSession(@PathVariable Integer id) throws ServiceException {
        liveSessionService.endLive(id);
        return JsonResponse.success();
    }

    @GetMapping("/sessions/{id}/push-url")
    @BackendPermission(slug = BPermissionConstant.LIVE_SESSION)
    public JsonResponse getPushUrl(@PathVariable Integer id) throws ServiceException {
        String pushUrl = liveSessionService.generatePushUrl(id);
        
        HashMap<String, Object> data = new HashMap<>();
        data.put("push_url", pushUrl);
        
        return JsonResponse.data(data);
    }

    @GetMapping("/sessions/{id}/play-url")
    public JsonResponse getPlayUrl(@PathVariable Integer id) throws ServiceException {
        String playUrl = liveSessionService.getPlayUrl(id);
        
        HashMap<String, Object> data = new HashMap<>();
        data.put("play_url", playUrl);
        
        return JsonResponse.data(data);
    }

    @PostMapping("/sessions/{id}/viewer-count")
    public JsonResponse updateViewerCount(
            @PathVariable Integer id,
            @RequestParam Integer count) throws ServiceException {
        
        liveSessionService.updateViewerCount(id, count);
        return JsonResponse.success();
    }
}
