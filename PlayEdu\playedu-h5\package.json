{"name": "playedu-h5-interface", "private": false, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^1.9.3", "ahooks": "^3.7.10", "antd-mobile": "^5.31.1", "axios": "^1.3.4", "localforage": "^1.10.0", "match-sorter": "^6.3.1", "moment": "^2.29.4", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.0.5", "react-router-dom": "^6.9.0", "redux": "^4.2.1", "sort-by": "^1.2.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-react-swc": "^3.0.0", "rollup-plugin-gzip": "^3.1.0", "sass": "^1.59.3", "terser": "^5.19.4", "typescript": "^4.9.3", "vite": "^4.2.0"}}