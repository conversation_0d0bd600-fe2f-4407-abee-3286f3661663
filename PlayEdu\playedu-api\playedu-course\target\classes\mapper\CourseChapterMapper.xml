<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.course.mapper.CourseChapterMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.course.domain.CourseChapter">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="courseId" column="course_id" jdbcType="INTEGER"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,course_id,name,
        sort,created_at,updated_at
    </sql>
</mapper>
