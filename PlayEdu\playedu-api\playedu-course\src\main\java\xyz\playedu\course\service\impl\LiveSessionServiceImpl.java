/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.course.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import xyz.playedu.common.types.paginate.PaginationResult;
import xyz.playedu.course.domain.LiveSession;
import xyz.playedu.course.mapper.LiveSessionMapper;
import xyz.playedu.course.service.LiveSessionService;

import java.util.Date;
import java.util.List;

/**
 * 直播场次服务实现类
 *
 * <AUTHOR> Team
 */
@Service
public class LiveSessionServiceImpl extends ServiceImpl<LiveSessionMapper, LiveSession> implements LiveSessionService {

    @Override
    public PaginationResult<LiveSession> paginate(int page, int size, String title, Integer status) {
        QueryWrapper<LiveSession> queryWrapper = new QueryWrapper<>();
        if (title != null && !title.trim().isEmpty()) {
            queryWrapper.like("title", title);
        }
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        queryWrapper.orderByDesc("created_at");

        IPage<LiveSession> pageResult = page(new Page<>(page, size), queryWrapper);
        PaginationResult<LiveSession> result = new PaginationResult<>();
        result.setTotal(pageResult.getTotal());
        result.setData(pageResult.getRecords());
        return result;
    }

    @Override
    public LiveSession create(String title, String description, Integer thumb, Date startTime,
                             Date endTime, Integer maxViewers, Integer isRecord, Integer adminId) {
        LiveSession liveSession = new LiveSession();
        liveSession.setTitle(title);
        liveSession.setDescription(description);
        liveSession.setThumb(thumb);
        liveSession.setStartTime(startTime);
        liveSession.setEndTime(endTime);
        liveSession.setMaxViewers(maxViewers);
        liveSession.setIsRecord(isRecord);
        liveSession.setAdminId(adminId);
        liveSession.setStatus(0); // 0: 未开始
        liveSession.setCurrentViewers(0);
        liveSession.setCreatedAt(new Date());
        liveSession.setUpdatedAt(new Date());
        save(liveSession);
        return liveSession;
    }

    @Override
    public void update(LiveSession liveSession, String title, String description, Integer thumb,
                      Date startTime, Date endTime, Integer maxViewers, Integer isRecord) {
        liveSession.setTitle(title);
        liveSession.setDescription(description);
        liveSession.setThumb(thumb);
        liveSession.setStartTime(startTime);
        liveSession.setEndTime(endTime);
        liveSession.setMaxViewers(maxViewers);
        liveSession.setIsRecord(isRecord);
        liveSession.setUpdatedAt(new Date());
        updateById(liveSession);
    }

    @Override
    public String startLive(Integer id) {
        LiveSession liveSession = getById(id);
        if (liveSession != null) {
            liveSession.setStatus(1); // 1: 直播中
            liveSession.setUpdatedAt(new Date());
            updateById(liveSession);
            return generatePushUrl(id);
        }
        return null;
    }

    @Override
    public void endLive(Integer id) {
        LiveSession liveSession = getById(id);
        if (liveSession != null) {
            liveSession.setStatus(2); // 2: 已结束
            liveSession.setUpdatedAt(new Date());
            updateById(liveSession);
        }
    }

    @Override
    public String getPlayUrl(Integer id) {
        return generatePullUrl(id);
    }

    @Override
    public void updateViewerCount(Integer id, Integer viewerCount) {
        LiveSession liveSession = getById(id);
        if (liveSession != null) {
            liveSession.setCurrentViewers(viewerCount);
            liveSession.setUpdatedAt(new Date());
            updateById(liveSession);
        }
    }

    @Override
    public List<LiveSession> getLivingSessions() {
        QueryWrapper<LiveSession> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1); // 1: 直播中
        return list(queryWrapper);
    }

    @Override
    public List<LiveSession> getUpcomingSessions(Integer minutes) {
        QueryWrapper<LiveSession> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 0); // 0: 未开始
        queryWrapper.le("start_time", new Date(System.currentTimeMillis() + minutes * 60 * 1000));
        return list(queryWrapper);
    }

    @Override
    public String generatePushUrl(Integer sessionId) {
        // 这里应该根据实际的直播服务提供商生成推流地址
        // 例如：阿里云直播、腾讯云直播等
        return "rtmp://push.example.com/live/session_" + sessionId + "?auth=token";
    }

    @Override
    public String generatePullUrl(Integer sessionId) {
        // 这里应该根据实际的直播服务提供商生成拉流地址
        return "rtmp://pull.example.com/live/session_" + sessionId;
    }

    @Override
    public String generateHlsUrl(Integer sessionId) {
        // 这里应该根据实际的直播服务提供商生成HLS播放地址
        return "https://hls.example.com/live/session_" + sessionId + ".m3u8";
    }
}
