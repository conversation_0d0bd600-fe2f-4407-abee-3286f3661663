/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.course.service;

import xyz.playedu.course.domain.ScormPackage;
import xyz.playedu.resource.domain.Resource;

/**
 * SCORM解析服务接口
 *
 * <AUTHOR> Team
 */
public interface ScormParseService {

    /**
     * 异步解析SCORM包
     *
     * @param scormPackage SCORM包信息
     * @param resource 资源信息
     */
    void parseScormPackageAsync(ScormPackage scormPackage, Resource resource);

    /**
     * 同步解析SCORM包
     *
     * @param scormPackage SCORM包信息
     * @param resource 资源信息
     * @return 是否解析成功
     */
    boolean parseScormPackage(ScormPackage scormPackage, Resource resource);

    /**
     * 解压SCORM包
     *
     * @param zipFilePath ZIP文件路径
     * @param extractPath 解压目录
     * @return 是否解压成功
     */
    boolean extractScormPackage(String zipFilePath, String extractPath);

    /**
     * 解析manifest.xml文件
     *
     * @param manifestPath manifest.xml文件路径
     * @return 解析结果
     */
    ScormManifest parseManifest(String manifestPath);

    /**
     * 验证SCORM包有效性
     *
     * @param extractPath 解压目录
     * @return 是否有效
     */
    boolean validateScormPackage(String extractPath);

    /**
     * 清理解析失败的文件
     *
     * @param extractPath 解压目录
     */
    void cleanupFailedParse(String extractPath);

    /**
     * SCORM Manifest 解析结果
     */
    class ScormManifest {
        private String identifier;
        private String version;
        private String scormVersion;
        private String title;
        private String description;
        private String launchFile;
        private String organizationJson;
        private String resourcesJson;
        private Integer timeLimit;
        private Double completionThreshold;
        private Double masteryScore;
        private Boolean supportsNavigation;

        // Getters and Setters
        public String getIdentifier() { return identifier; }
        public void setIdentifier(String identifier) { this.identifier = identifier; }

        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }

        public String getScormVersion() { return scormVersion; }
        public void setScormVersion(String scormVersion) { this.scormVersion = scormVersion; }

        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getLaunchFile() { return launchFile; }
        public void setLaunchFile(String launchFile) { this.launchFile = launchFile; }

        public String getOrganizationJson() { return organizationJson; }
        public void setOrganizationJson(String organizationJson) { this.organizationJson = organizationJson; }

        public String getResourcesJson() { return resourcesJson; }
        public void setResourcesJson(String resourcesJson) { this.resourcesJson = resourcesJson; }

        public Integer getTimeLimit() { return timeLimit; }
        public void setTimeLimit(Integer timeLimit) { this.timeLimit = timeLimit; }

        public Double getCompletionThreshold() { return completionThreshold; }
        public void setCompletionThreshold(Double completionThreshold) { this.completionThreshold = completionThreshold; }

        public Double getMasteryScore() { return masteryScore; }
        public void setMasteryScore(Double masteryScore) { this.masteryScore = masteryScore; }

        public Boolean getSupportsNavigation() { return supportsNavigation; }
        public void setSupportsNavigation(Boolean supportsNavigation) { this.supportsNavigation = supportsNavigation; }
    }
}
