/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.course.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SCORM包信息表
 * @TableName scorm_packages
 */
@TableName(value = "scorm_packages")
@Data
public class ScormPackage implements Serializable {
    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 资源ID */
    @JsonProperty("resource_id")
    private Integer resourceId;

    /** SCORM版本 */
    @JsonProperty("scorm_version")
    private String scormVersion;

    /** 包标识符 */
    private String identifier;

    /** 包标题 */
    private String title;

    /** 包描述 */
    private String description;

    /** 包版本 */
    private String version;

    /** 启动文件路径 */
    @JsonProperty("launch_file")
    private String launchFile;

    /** 解压后的目录路径 */
    @JsonProperty("extract_path")
    private String extractPath;

    /** manifest.xml内容 */
    @JsonProperty("manifest_content")
    private String manifestContent;

    /** 组织结构JSON */
    @JsonProperty("organization_json")
    private String organizationJson;

    /** 资源清单JSON */
    @JsonProperty("resources_json")
    private String resourcesJson;

    /** 是否支持导航 */
    @JsonProperty("supports_navigation")
    private Integer supportsNavigation;

    /** 最大时间限制（分钟） */
    @JsonProperty("time_limit")
    private Integer timeLimit;

    /** 完成阈值 */
    @JsonProperty("completion_threshold")
    private Double completionThreshold;

    /** 掌握分数 */
    @JsonProperty("mastery_score")
    private Double masteryScore;

    /** 解析状态：0-待解析，1-解析成功，2-解析失败 */
    @JsonProperty("parse_status")
    private Integer parseStatus;

    /** 解析错误信息 */
    @JsonProperty("parse_error")
    private String parseError;

    /** 创建时间 */
    @JsonProperty("created_at")
    private Date createdAt;

    /** 更新时间 */
    @JsonProperty("updated_at")
    private Date updatedAt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
