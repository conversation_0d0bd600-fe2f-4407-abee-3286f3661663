import { useEffect, useRef, useState } from "react";
import styles from "./media-player.module.scss";
import { useParams, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { course as Course } from "../../api/index";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { message, Spin } from "antd";
import { getPlayId, savePlayId } from "../../utils";
import AudioPlayer from "../../components/audio-player";
import DocumentViewer from "../../components/document-viewer";
import LivePlayer from "../../components/live-player";
import ScormPlayer from "../../components/scorm-player";

declare const window: any;
var timer: any = null;

interface CourseModel {
  id: number;
  title: string;
  thumb: string;
  short_desc: string;
  class_hour: number;
}

interface HourModel {
  id: number;
  title: string;
  type: string;
  duration: number;
  rid: number;
}

interface HourRecordModel {
  finished_duration: number;
  is_finished: number;
}

interface ResourceUrlModel {
  [key: number]: string;
}

const MediaPlayerPage = () => {
  const navigate = useNavigate();
  const params = useParams();
  const systemConfig = useSelector((state: any) => state.systemConfig.value);
  const user = useSelector((state: any) => state.loginUser.value.user);
  
  const [playUrl, setPlayUrl] = useState("");
  const [playDuration, setPlayDuration] = useState(0);
  const [playendedStatus, setPlayendedStatus] = useState(false);
  const [lastSeeValue, setLastSeeValue] = useState({});
  const [course, setCourse] = useState<CourseModel | null>(null);
  const [hour, setHour] = useState<HourModel | null>(null);
  const [loading, setLoading] = useState(false);
  const [isLastpage, setIsLastpage] = useState(false);
  const [totalHours, setTotalHours] = useState<HourModel[]>([]);
  const [playingTime, setPlayingTime] = useState(0);
  const [watchedSeconds, setWatchedSeconds] = useState(0);
  const [resourceUrl, setResourceUrl] = useState<ResourceUrlModel>({});
  const [extension, setExtension] = useState("");
  const [checkPlayerStatus, setCheckPlayerStatus] = useState(false);
  
  const myRef = useRef(0);
  const playRef = useRef(0);
  const watchRef = useRef(0);
  const totalRef = useRef(0);

  useEffect(() => {
    timer && clearInterval(timer);
    getCourse();
    getDetail();
    return () => {
      timer && clearInterval(timer);
    };
  }, [params.courseId, params.hourId]);

  useEffect(() => {
    myRef.current = playDuration;
  }, [playDuration]);

  useEffect(() => {
    playRef.current = playingTime;
  }, [playingTime]);

  useEffect(() => {
    watchRef.current = watchedSeconds;
  }, [watchedSeconds]);

  useEffect(() => {
    totalRef.current = hour?.duration || 0;
  }, [hour]);

  const getCourse = () => {
    Course.detail(Number(params.courseId)).then((res: any) => {
      setCourse(res.data.course);
      setTotalHours(res.data.hours);
      let hours = res.data.hours;
      if (hours.length > 0) {
        let currentIndex = hours.findIndex(
          (item: any) => item.id === Number(params.hourId)
        );
        if (currentIndex === hours.length - 1) {
          setIsLastpage(true);
        }
      }
    });
  };

  const getDetail = () => {
    if (loading) {
      return true;
    }
    setLoading(true);
    Course.play(Number(params.courseId), Number(params.hourId))
      .then((res: any) => {
        setCourse(res.data.course);
        setHour(res.data.hour);
        setExtension(res.data.extension || "");
        document.title = res.data.hour.title;
        
        let record: HourRecordModel = res.data.user_hour_record;
        let params = null;
        if (record && record.finished_duration && record.is_finished === 0) {
          params = {
            time: 5,
            pos: record.finished_duration,
          };
          setLastSeeValue(params);
          setWatchedSeconds(record.finished_duration);
        } else if (record && record.is_finished === 1) {
          setWatchedSeconds(res.data.hour.duration);
        }
        
        getMediaUrl(res.data.hour.rid, params);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
        message.error("加载失败");
      });
  };

  const getMediaUrl = (rid: number, data: any) => {
    Course.playUrl(Number(params.courseId), Number(params.hourId)).then(
      (res: any) => {
        setResourceUrl(res.data.resource_url);
        setPlayUrl(res.data.resource_url[rid]);
        savePlayId(String(params.courseId) + "-" + String(params.hourId));
        
        // 根据媒体类型初始化播放器
        if (hour?.type === "VIDEO") {
          initDPlayer(res.data.resource_url[rid], 0, data);
        }
      }
    );
  };

  const initDPlayer = (playUrl: string, isTrySee: number, params: any) => {
    let banDrag =
      systemConfig.playerIsDisabledDrag &&
      watchRef.current < totalRef.current &&
      watchRef.current === 0;
      
    window.player = new window.DPlayer({
      container: document.getElementById("meedu-player-container"),
      autoplay: false,
      video: {
        url: playUrl,
        pic: systemConfig.playerPoster,
      },
      try: isTrySee === 1,
      bulletSecret: {
        enabled: systemConfig.playerIsEnabledBulletSecret,
        text: systemConfig.playerBulletSecretText
          .replace("{name}", user.name)
          .replace("{email}", user.email)
          .replace("{idCard}", user.id_card),
        size: "14px",
        color: systemConfig.playerBulletSecretColor || "red",
        opacity: Number(systemConfig.playerBulletSecretOpacity),
      },
      ban_drag: banDrag,
      last_see_pos: params,
    });

    window.player.on("timeupdate", () => {
      setPlayingTime(parseInt(window.player.video.currentTime));
      playTimeUpdate(parseInt(window.player.video.currentTime), false);
    });

    window.player.on("ended", () => {
      if (
        systemConfig.playerIsDisabledDrag &&
        watchRef.current < totalRef.current &&
        window.player.video.duration - playRef.current >= 2
      ) {
        window.player.seek(playRef.current);
        return;
      }
      setPlayingTime(0);
      setPlayendedStatus(true);
      playTimeUpdate(parseInt(window.player.video.currentTime), true);
      window.player && window.player.destroy();
    });
    
    setLoading(false);
    checkPlayer();
  };

  const playTimeUpdate = (duration: number, isEnd: boolean) => {
    if (duration - myRef.current >= 10 || isEnd === true) {
      setPlayDuration(duration);
      Course.record(
        Number(params.courseId),
        Number(params.hourId),
        duration
      ).then((res: any) => {});
      Course.playPing(Number(params.courseId), Number(params.hourId)).then(
        (res: any) => {}
      );
    }
  };

  const checkPlayer = () => {
    timer = setInterval(() => {
      let playId = getPlayId();
      if (
        playId &&
        playId !== String(params.courseId) + "-" + String(params.hourId)
      ) {
        timer && clearInterval(timer);
        window.player && window.player.destroy();
        setCheckPlayerStatus(true);
      } else {
        setCheckPlayerStatus(false);
      }
    }, 5000);
  };

  const goNextVideo = () => {
    let currentIndex = totalHours.findIndex(
      (item: any) => item.id === Number(params.hourId)
    );
    if (currentIndex < totalHours.length - 1) {
      let nextHour = totalHours[currentIndex + 1];
      navigate(
        `/course/${params.courseId}/hour/${nextHour.id}`,
        { replace: true }
      );
    }
  };

  const handleAudioTimeUpdate = (currentTime: number, duration: number) => {
    playTimeUpdate(Math.floor(currentTime), false);
  };

  const handleAudioEnded = () => {
    setPlayendedStatus(true);
    playTimeUpdate(hour?.duration || 0, true);
  };

  const handleDocumentDownload = () => {
    if (hour?.rid) {
      Course.downloadAttachment(Number(params.courseId), hour.rid);
    }
  };

  const renderMediaPlayer = () => {
    if (!hour || !playUrl) {
      return null;
    }

    switch (hour.type) {
      case "VIDEO":
        return (
          <div
            className="play-box"
            id="meedu-player-container"
            style={{ borderRadius: 8 }}
          />
        );
        
      case "AUDIO":
        return (
          <AudioPlayer
            src={playUrl}
            title={hour.title}
            onTimeUpdate={handleAudioTimeUpdate}
            onEnded={handleAudioEnded}
            showTitle={false}
          />
        );
        
      case "PDF":
      case "WORD":
      case "PPT":
      case "EXCEL":
      case "TXT":
      case "H5":
        return (
          <DocumentViewer
            src={playUrl}
            title={hour.title}
            extension={extension}
            onDownload={handleDocumentDownload}
            height="600px"
          />
        );
        
      case "LIVE":
        return (
          <LivePlayer
            src={playUrl}
            title={hour.title}
            status={1} // 假设直播中
            viewerCount={0}
            height="600px"
          />
        );

      case "SCORM":
        return (
          <ScormPlayer
            packageId={hour.rid}
            attemptId={0} // 需要从API获取
            launchUrl={playUrl}
            title={hour.title}
            height="600px"
            onProgress={(progress) => {
              // 处理学习进度
              console.log("SCORM学习进度:", progress);
            }}
            onComplete={() => {
              // 处理完成事件
              setPlayendedStatus(true);
            }}
            onError={(error) => {
              console.error("SCORM播放错误:", error);
            }}
          />
        );

      default:
        return (
          <div className={styles["unsupported-media"]}>
            <p>不支持的媒体类型: {hour.type}</p>
          </div>
        );
    }
  };

  return (
    <div className={styles["media-mask"]}>
      <div className={styles["top-cont"]}>
        <div className={styles["box"]}>
          <div
            className={styles["close-btn"]}
            onClick={() => {
              timer && clearInterval(timer);
              window.player && window.player.destroy();
              navigate(-1);
            }}
          >
            <ArrowLeftOutlined />
            <span className="ml-14">返回</span>
          </div>
        </div>
      </div>
      
      <div className={styles["media-body"]}>
        <div className={styles["media-title"]}>{hour?.title}</div>
        <div className={styles["media-box"]}>
          {loading ? (
            <div className={styles["loading-container"]}>
              <Spin size="large" tip="加载中..." />
            </div>
          ) : (
            renderMediaPlayer()
          )}
          
          {checkPlayerStatus && (
            <div className={styles["alert-message"]}>
              <div className={styles["des-media"]}>
                您已打开新内容，暂停本内容播放
              </div>
            </div>
          )}
          
          {playendedStatus && (
            <div className={styles["alert-message"]}>
              {isLastpage ? (
                <div
                  className={styles["alert-button"]}
                  onClick={() => navigate(-1)}
                >
                  恭喜你学完最后一节
                </div>
              ) : (
                <div
                  className={styles["alert-button"]}
                  onClick={() => {
                    setLastSeeValue({});
                    setPlayendedStatus(false);
                    goNextVideo();
                  }}
                >
                  播放下一节
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MediaPlayerPage;
