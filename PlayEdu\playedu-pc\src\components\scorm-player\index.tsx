import React, { useEffect, useRef, useState } from "react";
import { Spin, Alert, Button, Space, Typography, Progress } from "antd";
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  BookOutlined,
} from "@ant-design/icons";
import styles from "./index.module.less";

const { Title, Text } = Typography;

interface ScormPlayerProps {
  packageId: number;
  attemptId: number;
  launchUrl: string;
  title?: string;
  onProgress?: (progress: number) => void;
  onComplete?: () => void;
  onError?: (error: string) => void;
  height?: number | string;
}

interface ScormAPI {
  Initialize: (parameter: string) => string;
  Terminate: (parameter: string) => string;
  GetValue: (element: string) => string;
  SetValue: (element: string, value: string) => string;
  Commit: (parameter: string) => string;
  GetLastError: () => string;
  GetErrorString: (errorCode: string) => string;
  GetDiagnostic: (errorCode: string) => string;
}

const ScormPlayer: React.FC<ScormPlayerProps> = ({
  packageId,
  attemptId,
  launchUrl,
  title,
  onProgress,
  onComplete,
  onError,
  height = "600px",
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState("not attempted");
  const [score, setScore] = useState<number | null>(null);
  const [sessionTime, setSessionTime] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // SCORM API 实现
  const scormAPI: ScormAPI = {
    Initialize: (parameter: string) => {
      console.log("SCORM API: Initialize called with", parameter);
      setIsInitialized(true);
      return "true";
    },

    Terminate: (parameter: string) => {
      console.log("SCORM API: Terminate called with", parameter);
      setIsInitialized(false);
      return "true";
    },

    GetValue: (element: string) => {
      console.log("SCORM API: GetValue called with", element);
      
      switch (element) {
        case "cmi.core.lesson_status":
        case "cmi.completion_status":
          return status;
        case "cmi.core.score.raw":
        case "cmi.score.raw":
          return score?.toString() || "";
        case "cmi.core.student_id":
        case "cmi.learner_id":
          return "student_001"; // 从用户信息获取
        case "cmi.core.student_name":
        case "cmi.learner_name":
          return "学习者"; // 从用户信息获取
        case "cmi.core.lesson_location":
        case "cmi.location":
          return ""; // 书签位置
        case "cmi.core.entry":
        case "cmi.entry":
          return "ab-initio";
        case "cmi.mode":
          return "normal";
        case "cmi.credit":
          return "credit";
        default:
          return "";
      }
    },

    SetValue: (element: string, value: string) => {
      console.log("SCORM API: SetValue called with", element, value);
      
      switch (element) {
        case "cmi.core.lesson_status":
        case "cmi.completion_status":
          setStatus(value);
          if (value === "completed" || value === "passed") {
            onComplete?.();
          }
          break;
        case "cmi.core.score.raw":
        case "cmi.score.raw":
          const scoreValue = parseFloat(value);
          setScore(scoreValue);
          break;
        case "cmi.progress_measure":
          const progressValue = parseFloat(value) * 100;
          setProgress(progressValue);
          onProgress?.(progressValue);
          break;
        case "cmi.core.session_time":
        case "cmi.session_time":
          // 解析时间格式并更新
          break;
      }
      
      return "true";
    },

    Commit: (parameter: string) => {
      console.log("SCORM API: Commit called with", parameter);
      // 发送数据到服务器
      this.saveScormData();
      return "true";
    },

    GetLastError: () => {
      return "0"; // 无错误
    },

    GetErrorString: (errorCode: string) => {
      const errorMessages: { [key: string]: string } = {
        "0": "无错误",
        "101": "一般异常",
        "201": "无效参数",
        "301": "未初始化",
        "401": "未实现",
        "403": "数据模型元素值未初始化",
        "404": "数据模型元素不存在",
      };
      return errorMessages[errorCode] || "未知错误";
    },

    GetDiagnostic: (errorCode: string) => {
      return `诊断信息: ${errorCode}`;
    },

    saveScormData: async () => {
      try {
        // 发送学习数据到服务器
        const response = await fetch(`/api/scorm/attempts/${attemptId}/commit`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            status,
            score,
            progress,
            sessionTime,
          }),
        });
        
        if (!response.ok) {
          throw new Error("保存学习数据失败");
        }
      } catch (error) {
        console.error("保存SCORM数据失败:", error);
        onError?.("保存学习数据失败");
      }
    },
  };

  useEffect(() => {
    // 将SCORM API暴露给iframe中的内容
    (window as any).API = scormAPI;
    (window as any).API_1484_11 = scormAPI; // SCORM 2004
    
    // 清理函数
    return () => {
      delete (window as any).API;
      delete (window as any).API_1484_11;
    };
  }, []);

  useEffect(() => {
    // 定时更新会话时间
    const timer = setInterval(() => {
      if (isInitialized) {
        setSessionTime(prev => prev + 1);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [isInitialized]);

  const handleIframeLoad = () => {
    setLoading(false);
    
    // 检查iframe中的内容是否能访问SCORM API
    const iframe = iframeRef.current;
    if (iframe && iframe.contentWindow) {
      try {
        // 为iframe内容提供SCORM API访问
        (iframe.contentWindow as any).parent.API = scormAPI;
        (iframe.contentWindow as any).parent.API_1484_11 = scormAPI;
      } catch (e) {
        console.warn("无法为iframe设置SCORM API:", e);
      }
    }
  };

  const handleIframeError = () => {
    setLoading(false);
    setError("SCORM内容加载失败");
    onError?.("SCORM内容加载失败");
  };

  const handleReload = () => {
    setLoading(true);
    setError(null);
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
    }
  };

  const handleFullscreen = () => {
    const container = document.querySelector(`.${styles["scorm-container"]}`);
    if (container) {
      if (!isFullscreen) {
        if (container.requestFullscreen) {
          container.requestFullscreen();
          setIsFullscreen(true);
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
          setIsFullscreen(false);
        }
      }
    }
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
      case "passed":
        return "#52c41a";
      case "failed":
        return "#ff4d4f";
      case "incomplete":
        return "#faad14";
      default:
        return "#d9d9d9";
    }
  };

  return (
    <div className={styles["scorm-player"]}>
      {title && (
        <div className={styles["scorm-header"]}>
          <div className={styles["scorm-info"]}>
            <Title level={4}>
              <BookOutlined /> {title}
            </Title>
            <Space>
              <Text>状态: </Text>
              <span style={{ color: getStatusColor(status) }}>
                {status === "not attempted" && "未开始"}
                {status === "incomplete" && "进行中"}
                {status === "completed" && "已完成"}
                {status === "passed" && "通过"}
                {status === "failed" && "失败"}
              </span>
              {score !== null && (
                <>
                  <Text>分数: {score}</Text>
                </>
              )}
              <Text>学习时间: {formatTime(sessionTime)}</Text>
            </Space>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReload}
              disabled={loading}
            >
              重新加载
            </Button>
            <Button
              icon={<FullscreenOutlined />}
              onClick={handleFullscreen}
            >
              {isFullscreen ? "退出全屏" : "全屏"}
            </Button>
          </Space>
        </div>
      )}

      {progress > 0 && (
        <div className={styles["progress-bar"]}>
          <Progress
            percent={Math.round(progress)}
            status={status === "completed" ? "success" : "active"}
            strokeColor={getStatusColor(status)}
          />
        </div>
      )}

      <div className={styles["scorm-container"]} style={{ height }}>
        {loading && (
          <div className={styles["loading-overlay"]}>
            <Spin size="large" tip="SCORM内容加载中..." />
          </div>
        )}

        {error && (
          <div className={styles["error-overlay"]}>
            <Alert
              message="加载失败"
              description={error}
              type="error"
              showIcon
              action={
                <Button size="small" onClick={handleReload}>
                  重新加载
                </Button>
              }
            />
          </div>
        )}

        <iframe
          ref={iframeRef}
          src={launchUrl}
          width="100%"
          height="100%"
          frameBorder="0"
          onLoad={handleIframeLoad}
          onError={handleIframeError}
          className={styles["scorm-iframe"]}
          sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
          title="SCORM Content"
        />
      </div>
    </div>
  );
};

export default ScormPlayer;
