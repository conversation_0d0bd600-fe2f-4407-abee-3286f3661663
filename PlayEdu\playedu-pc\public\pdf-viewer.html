<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF预览器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            overflow: hidden;
        }

        .pdf-viewer {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .toolbar {
            background: #fff;
            border-bottom: 1px solid #e8e8e8;
            padding: 8px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .btn {
            background: #fff;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn.primary {
            background: #1890ff;
            border-color: #1890ff;
            color: #fff;
        }

        .btn.primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }

        .page-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #666;
        }

        .page-input {
            width: 50px;
            padding: 2px 6px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
        }

        .scale-select {
            padding: 2px 6px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;
            background: #fff;
        }

        .viewer-container {
            flex: 1;
            overflow: auto;
            background: #525659;
            position: relative;
        }

        .pdf-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            min-height: 100%;
        }

        .pdf-page {
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            background: #fff;
            position: relative;
        }

        .pdf-page canvas {
            display: block;
            max-width: 100%;
            height: auto;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .loading::before {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #fff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            text-align: center;
            background: rgba(0, 0, 0, 0.7);
            padding: 20px;
            border-radius: 8px;
        }

        .error h3 {
            margin-bottom: 8px;
            color: #ff4d4f;
        }

        .search-box {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 8px;
            border-radius: 4px;
            display: none;
        }

        .search-box.show {
            display: block;
        }

        .search-input {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;
            width: 150px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .toolbar {
                padding: 6px 12px;
                flex-wrap: wrap;
                gap: 8px;
            }

            .toolbar-left,
            .toolbar-right {
                gap: 8px;
            }

            .btn {
                padding: 3px 6px;
                font-size: 11px;
            }

            .page-info {
                font-size: 11px;
            }

            .pdf-container {
                padding: 10px;
            }

            .pdf-page {
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="pdf-viewer">
        <div class="toolbar">
            <div class="toolbar-left">
                <button class="btn" id="prevPage" title="上一页">
                    ◀ 上一页
                </button>
                <button class="btn" id="nextPage" title="下一页">
                    下一页 ▶
                </button>
                <div class="page-info">
                    <input type="number" class="page-input" id="pageInput" min="1" value="1">
                    <span>/ <span id="totalPages">0</span></span>
                </div>
            </div>
            <div class="toolbar-right">
                <select class="scale-select" id="scaleSelect">
                    <option value="auto">自适应</option>
                    <option value="page-fit">适合页面</option>
                    <option value="page-width">适合宽度</option>
                    <option value="0.5">50%</option>
                    <option value="0.75">75%</option>
                    <option value="1">100%</option>
                    <option value="1.25">125%</option>
                    <option value="1.5">150%</option>
                    <option value="2">200%</option>
                </select>
                <button class="btn" id="zoomOut" title="缩小">
                    🔍-
                </button>
                <button class="btn" id="zoomIn" title="放大">
                    🔍+
                </button>
                <button class="btn" id="searchBtn" title="搜索">
                    🔍
                </button>
                <button class="btn" id="downloadBtn" title="下载">
                    ⬇ 下载
                </button>
            </div>
        </div>
        
        <div class="viewer-container" id="viewerContainer">
            <div class="pdf-container" id="pdfContainer">
                <div class="loading" id="loading">
                    正在加载PDF文档...
                </div>
            </div>
            
            <div class="search-box" id="searchBox">
                <input type="text" class="search-input" id="searchInput" placeholder="搜索文本...">
                <button class="btn" id="searchPrev">上一个</button>
                <button class="btn" id="searchNext">下一个</button>
                <button class="btn" id="closeSearch">✕</button>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>
        // PDF.js 配置
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

        class PDFViewer {
            constructor() {
                this.pdfDoc = null;
                this.currentPage = 1;
                this.totalPages = 0;
                this.scale = 1.0;
                this.isLoading = false;
                
                this.initElements();
                this.bindEvents();
                this.loadPDF();
            }

            initElements() {
                this.container = document.getElementById('pdfContainer');
                this.loading = document.getElementById('loading');
                this.prevBtn = document.getElementById('prevPage');
                this.nextBtn = document.getElementById('nextPage');
                this.pageInput = document.getElementById('pageInput');
                this.totalPagesSpan = document.getElementById('totalPages');
                this.scaleSelect = document.getElementById('scaleSelect');
                this.zoomInBtn = document.getElementById('zoomIn');
                this.zoomOutBtn = document.getElementById('zoomOut');
                this.searchBtn = document.getElementById('searchBtn');
                this.downloadBtn = document.getElementById('downloadBtn');
                this.searchBox = document.getElementById('searchBox');
                this.searchInput = document.getElementById('searchInput');
            }

            bindEvents() {
                this.prevBtn.addEventListener('click', () => this.prevPage());
                this.nextBtn.addEventListener('click', () => this.nextPage());
                this.pageInput.addEventListener('change', (e) => this.goToPage(parseInt(e.target.value)));
                this.scaleSelect.addEventListener('change', (e) => this.setScale(e.target.value));
                this.zoomInBtn.addEventListener('click', () => this.zoomIn());
                this.zoomOutBtn.addEventListener('click', () => this.zoomOut());
                this.searchBtn.addEventListener('click', () => this.toggleSearch());
                this.downloadBtn.addEventListener('click', () => this.downloadPDF());
                
                // 键盘快捷键
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey || e.metaKey) {
                        switch(e.key) {
                            case '=':
                            case '+':
                                e.preventDefault();
                                this.zoomIn();
                                break;
                            case '-':
                                e.preventDefault();
                                this.zoomOut();
                                break;
                            case 'f':
                                e.preventDefault();
                                this.toggleSearch();
                                break;
                        }
                    } else {
                        switch(e.key) {
                            case 'ArrowLeft':
                                this.prevPage();
                                break;
                            case 'ArrowRight':
                                this.nextPage();
                                break;
                        }
                    }
                });
            }

            async loadPDF() {
                const urlParams = new URLSearchParams(window.location.search);
                const fileUrl = urlParams.get('file');
                
                if (!fileUrl) {
                    this.showError('未指定PDF文件');
                    return;
                }

                try {
                    this.isLoading = true;
                    this.pdfDoc = await pdfjsLib.getDocument(fileUrl).promise;
                    this.totalPages = this.pdfDoc.numPages;
                    this.totalPagesSpan.textContent = this.totalPages;
                    this.pageInput.max = this.totalPages;
                    
                    this.loading.style.display = 'none';
                    this.renderAllPages();
                    this.updateButtons();
                } catch (error) {
                    console.error('PDF加载失败:', error);
                    this.showError('PDF文件加载失败，请检查文件是否存在或格式是否正确');
                }
            }

            async renderAllPages() {
                this.container.innerHTML = '';
                
                for (let pageNum = 1; pageNum <= this.totalPages; pageNum++) {
                    const pageDiv = document.createElement('div');
                    pageDiv.className = 'pdf-page';
                    pageDiv.id = `page-${pageNum}`;
                    
                    const canvas = document.createElement('canvas');
                    pageDiv.appendChild(canvas);
                    this.container.appendChild(pageDiv);
                    
                    await this.renderPage(pageNum, canvas);
                }
            }

            async renderPage(pageNum, canvas) {
                try {
                    const page = await this.pdfDoc.getPage(pageNum);
                    const viewport = page.getViewport({ scale: this.scale });
                    
                    canvas.width = viewport.width;
                    canvas.height = viewport.height;
                    
                    const ctx = canvas.getContext('2d');
                    await page.render({ canvasContext: ctx, viewport: viewport }).promise;
                } catch (error) {
                    console.error(`页面 ${pageNum} 渲染失败:`, error);
                }
            }

            prevPage() {
                if (this.currentPage > 1) {
                    this.currentPage--;
                    this.scrollToPage(this.currentPage);
                    this.updateButtons();
                }
            }

            nextPage() {
                if (this.currentPage < this.totalPages) {
                    this.currentPage++;
                    this.scrollToPage(this.currentPage);
                    this.updateButtons();
                }
            }

            goToPage(pageNum) {
                if (pageNum >= 1 && pageNum <= this.totalPages) {
                    this.currentPage = pageNum;
                    this.scrollToPage(this.currentPage);
                    this.updateButtons();
                }
            }

            scrollToPage(pageNum) {
                const pageElement = document.getElementById(`page-${pageNum}`);
                if (pageElement) {
                    pageElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }

            setScale(scaleValue) {
                if (scaleValue === 'auto' || scaleValue === 'page-fit' || scaleValue === 'page-width') {
                    // 这些选项需要根据容器大小计算
                    this.scale = 1.0;
                } else {
                    this.scale = parseFloat(scaleValue);
                }
                this.renderAllPages();
            }

            zoomIn() {
                this.scale = Math.min(this.scale * 1.25, 3.0);
                this.scaleSelect.value = this.scale.toString();
                this.renderAllPages();
            }

            zoomOut() {
                this.scale = Math.max(this.scale / 1.25, 0.25);
                this.scaleSelect.value = this.scale.toString();
                this.renderAllPages();
            }

            toggleSearch() {
                this.searchBox.classList.toggle('show');
                if (this.searchBox.classList.contains('show')) {
                    this.searchInput.focus();
                }
            }

            downloadPDF() {
                const urlParams = new URLSearchParams(window.location.search);
                const fileUrl = urlParams.get('file');
                if (fileUrl) {
                    const link = document.createElement('a');
                    link.href = fileUrl;
                    link.download = 'document.pdf';
                    link.click();
                }
            }

            updateButtons() {
                this.prevBtn.disabled = this.currentPage <= 1;
                this.nextBtn.disabled = this.currentPage >= this.totalPages;
                this.pageInput.value = this.currentPage;
            }

            showError(message) {
                this.loading.style.display = 'none';
                this.container.innerHTML = `
                    <div class="error">
                        <h3>加载失败</h3>
                        <p>${message}</p>
                        <button class="btn primary" onclick="window.location.reload()">重新加载</button>
                    </div>
                `;
            }
        }

        // 初始化PDF查看器
        document.addEventListener('DOMContentLoaded', () => {
            new PDFViewer();
        });
    </script>
</body>
</html>
