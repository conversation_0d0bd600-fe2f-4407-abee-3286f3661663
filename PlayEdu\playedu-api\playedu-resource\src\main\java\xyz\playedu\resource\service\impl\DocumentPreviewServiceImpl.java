/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.resource.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import xyz.playedu.common.constant.BackendConstant;
import xyz.playedu.common.service.AppConfigService;
import xyz.playedu.common.types.config.S3Config;
import xyz.playedu.common.util.S3Util;
import xyz.playedu.common.util.StringUtil;
import xyz.playedu.resource.domain.Resource;
import xyz.playedu.resource.service.DocumentPreviewService;

import java.util.Arrays;
import java.util.List;

/**
 * 文档预览服务实现
 * 
 * <AUTHOR> Team
 */
@Service
@Slf4j
public class DocumentPreviewServiceImpl implements DocumentPreviewService {

    @Autowired
    private AppConfigService appConfigService;

    // 支持预览的文档格式
    private static final List<String> PREVIEW_SUPPORTED_EXTENSIONS = Arrays.asList(
            "pdf", "doc", "docx", "ppt", "pptx", "xls", "xlsx", "txt"
    );

    // 支持PDF.js预览的格式
    private static final List<String> PDFJS_SUPPORTED_EXTENSIONS = Arrays.asList("pdf");

    // 支持Office Online预览的格式
    private static final List<String> OFFICE_ONLINE_SUPPORTED_EXTENSIONS = Arrays.asList(
            "doc", "docx", "ppt", "pptx", "xls", "xlsx"
    );

    @Override
    public String getPreviewUrl(Resource resource) {
        if (resource == null || StringUtil.isEmpty(resource.getExtension())) {
            return null;
        }

        String extension = resource.getExtension().toLowerCase();
        
        // PDF文件直接使用PDF.js预览
        if (PDFJS_SUPPORTED_EXTENSIONS.contains(extension)) {
            return getPdfJsUrl(resource);
        }
        
        // Office文件使用Office Online预览
        if (OFFICE_ONLINE_SUPPORTED_EXTENSIONS.contains(extension)) {
            return getOfficeOnlineUrl(resource);
        }
        
        // 文本文件直接返回文件URL
        if ("txt".equals(extension)) {
            S3Config s3Config = appConfigService.getS3Config();
            return new S3Util(s3Config).generateEndpointPreSignUrl(resource.getPath());
        }
        
        return null;
    }

    @Override
    public boolean isSupportPreview(String extension) {
        if (StringUtil.isEmpty(extension)) {
            return false;
        }
        return PREVIEW_SUPPORTED_EXTENSIONS.contains(extension.toLowerCase());
    }

    @Override
    public Integer convertToPdf(Resource resource) {
        // TODO: 实现文档转PDF功能
        // 可以使用LibreOffice、Apache POI等工具进行转换
        log.info("Converting document to PDF: {}", resource.getName());
        return null;
    }

    @Override
    public Integer getPageCount(Resource resource) {
        // TODO: 实现获取文档页数功能
        log.info("Getting page count for document: {}", resource.getName());
        return 1;
    }

    @Override
    public String getThumbnail(Resource resource, Integer page) {
        // TODO: 实现生成文档缩略图功能
        log.info("Getting thumbnail for document: {}, page: {}", resource.getName(), page);
        return null;
    }

    @Override
    public String getOfficeOnlineUrl(Resource resource) {
        if (resource == null) {
            return null;
        }

        S3Config s3Config = appConfigService.getS3Config();
        String fileUrl = new S3Util(s3Config).generateEndpointPreSignUrl(resource.getPath());
        
        // 使用Microsoft Office Online Viewer
        // 注意：生产环境中需要配置正确的Office Online服务器
        String officeOnlineBaseUrl = "https://view.officeapps.live.com/op/embed.aspx";
        return officeOnlineBaseUrl + "?src=" + java.net.URLEncoder.encode(fileUrl, java.nio.charset.StandardCharsets.UTF_8);
    }

    @Override
    public String getPdfJsUrl(Resource resource) {
        if (resource == null) {
            return null;
        }

        S3Config s3Config = appConfigService.getS3Config();
        String fileUrl = new S3Util(s3Config).generateEndpointPreSignUrl(resource.getPath());
        
        // 返回PDF.js预览URL
        // 前端需要集成PDF.js库
        return "/pdf-viewer?file=" + java.net.URLEncoder.encode(fileUrl, java.nio.charset.StandardCharsets.UTF_8);
    }
}
