import React, { useEffect, useRef, useState } from "react";
import { Button, Space, Typography, Badge, Spin, Alert } from "antd";
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  FullscreenOutlined,
  SoundOutlined,
  MutedOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import styles from "./index.module.less";

const { Title, Text } = Typography;

interface LivePlayerProps {
  src: string;
  title?: string;
  status: number; // 0-未开始，1-直播中，2-已结束
  viewerCount?: number;
  onViewerJoin?: () => void;
  onViewerLeave?: () => void;
  showChat?: boolean;
  height?: number | string;
}

const LivePlayer: React.FC<LivePlayerProps> = ({
  src,
  title,
  status,
  viewerCount = 0,
  onViewerJoin,
  onViewerLeave,
  showChat = true,
  height = "400px",
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadStart = () => {
      setLoading(true);
      setError(null);
    };

    const handleCanPlay = () => {
      setLoading(false);
    };

    const handlePlay = () => {
      setIsPlaying(true);
      onViewerJoin?.();
    };

    const handlePause = () => {
      setIsPlaying(false);
    };

    const handleError = () => {
      setLoading(false);
      setError("直播加载失败，请检查网络连接或稍后重试");
    };

    const handleEnded = () => {
      setIsPlaying(false);
      onViewerLeave?.();
    };

    video.addEventListener("loadstart", handleLoadStart);
    video.addEventListener("canplay", handleCanPlay);
    video.addEventListener("play", handlePlay);
    video.addEventListener("pause", handlePause);
    video.addEventListener("error", handleError);
    video.addEventListener("ended", handleEnded);

    return () => {
      video.removeEventListener("loadstart", handleLoadStart);
      video.removeEventListener("canplay", handleCanPlay);
      video.removeEventListener("play", handlePlay);
      video.removeEventListener("pause", handlePause);
      video.removeEventListener("error", handleError);
      video.removeEventListener("ended", handleEnded);
    };
  }, [onViewerJoin, onViewerLeave]);

  useEffect(() => {
    // 当直播状态改变时，重新加载视频
    if (status === 1 && videoRef.current) {
      videoRef.current.load();
    }
  }, [status, src]);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !isMuted;
    setIsMuted(!isMuted);
  };

  const toggleFullscreen = () => {
    const video = videoRef.current;
    if (!video) return;

    if (video.requestFullscreen) {
      video.requestFullscreen();
    }
  };

  const getStatusBadge = () => {
    switch (status) {
      case 0:
        return <Badge status="default" text="未开始" />;
      case 1:
        return <Badge status="processing" text="直播中" />;
      case 2:
        return <Badge status="error" text="已结束" />;
      default:
        return <Badge status="default" text="未知状态" />;
    }
  };

  const renderPlayer = () => {
    if (status === 0) {
      return (
        <div className={styles["waiting-container"]}>
          <div className={styles["waiting-content"]}>
            <Title level={4}>直播尚未开始</Title>
            <Text type="secondary">请耐心等待主播开播</Text>
          </div>
        </div>
      );
    }

    if (status === 2) {
      return (
        <div className={styles["ended-container"]}>
          <div className={styles["ended-content"]}>
            <Title level={4}>直播已结束</Title>
            <Text type="secondary">感谢您的观看</Text>
          </div>
        </div>
      );
    }

    return (
      <div className={styles["video-container"]}>
        {loading && (
          <div className={styles["loading-overlay"]}>
            <Spin size="large" tip="直播加载中..." />
          </div>
        )}

        {error && (
          <div className={styles["error-overlay"]}>
            <Alert
              message="直播加载失败"
              description={error}
              type="error"
              showIcon
              action={
                <Button size="small" onClick={() => window.location.reload()}>
                  重新加载
                </Button>
              }
            />
          </div>
        )}

        <video
          ref={videoRef}
          src={src}
          width="100%"
          height="100%"
          controls={false}
          autoPlay
          muted={isMuted}
          playsInline
          className={styles["video-element"]}
        />

        <div className={styles["video-controls"]}>
          <Space>
            <Button
              type="text"
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={togglePlay}
              className={styles["control-button"]}
            />
            
            <Button
              type="text"
              icon={isMuted ? <MutedOutlined /> : <SoundOutlined />}
              onClick={toggleMute}
              className={styles["control-button"]}
            />
            
            <Button
              type="text"
              icon={<FullscreenOutlined />}
              onClick={toggleFullscreen}
              className={styles["control-button"]}
            />
          </Space>
        </div>
      </div>
    );
  };

  return (
    <div className={styles["live-player"]}>
      <div className={styles["live-header"]}>
        <div className={styles["live-info"]}>
          {title && <Title level={4}>{title}</Title>}
          <Space>
            {getStatusBadge()}
            <Space>
              <EyeOutlined />
              <Text>{viewerCount} 人观看</Text>
            </Space>
          </Space>
        </div>
      </div>

      <div className={styles["live-content"]} style={{ height }}>
        <div className={styles["player-area"]}>
          {renderPlayer()}
        </div>

        {showChat && (
          <div className={styles["chat-area"]}>
            <div className={styles["chat-header"]}>
              <Title level={5}>聊天室</Title>
            </div>
            <div className={styles["chat-messages"]}>
              <Text type="secondary">聊天功能开发中...</Text>
            </div>
            <div className={styles["chat-input"]}>
              {/* 聊天输入框组件 */}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LivePlayer;
