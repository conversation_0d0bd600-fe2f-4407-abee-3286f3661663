<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="playedu-resource" />
        <module name="playedu-system" />
        <module name="playedu-api" />
        <module name="playedu-common" />
        <module name="playedu-course" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="playedu" target="17" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="playedu" options="-parameters" />
      <module name="playedu-api" options="-parameters" />
      <module name="playedu-common" options="-parameters" />
      <module name="playedu-course" options="-parameters" />
      <module name="playedu-resource" options="-parameters" />
      <module name="playedu-system" options="-parameters" />
    </option>
  </component>
</project>