/**
 * SCORM API适配器
 * 为SCORM包提供标准的SCORM API接口，与PlayEdu后端进行数据交互
 */

// SCORM API配置
const SCORM_CONFIG = {
    apiUrl: 'http://localhost:9898/api/v1/scorm',
    debug: true
};

// SCORM API状态
let scormState = {
    initialized: false,
    terminated: false,
    packageId: null,
    attemptId: null,
    lastError: '0',
    errorString: ''
};

// 错误代码映射
const ERROR_CODES = {
    NO_ERROR: '0',
    GENERAL_EXCEPTION: '101',
    GENERAL_INITIALIZATION_FAILURE: '102',
    ALREADY_INITIALIZED: '103',
    CONTENT_INSTANCE_TERMINATED: '104',
    GENERAL_TERMINATION_FAILURE: '111',
    TERMINATION_BEFORE_INITIALIZATION: '112',
    TERMINATION_AFTER_TERMINATION: '113',
    RETRIEVE_DATA_BEFORE_INITIALIZATION: '122',
    RETRIEVE_DATA_AFTER_TERMINATION: '123',
    STORE_DATA_BEFORE_INITIALIZATION: '132',
    STORE_DATA_AFTER_TERMINATION: '133',
    COMMIT_BEFORE_INITIALIZATION: '142',
    COMMIT_AFTER_TERMINATION: '143',
    GENERAL_ARGUMENT_ERROR: '201',
    GENERAL_GET_FAILURE: '301',
    GENERAL_SET_FAILURE: '351',
    GENERAL_COMMIT_FAILURE: '391',
    UNDEFINED_DATA_MODEL: '401',
    UNIMPLEMENTED_DATA_MODEL: '402',
    DATA_MODEL_VALUE_NOT_INITIALIZED: '403',
    DATA_MODEL_VALUE_IS_READ_ONLY: '404',
    DATA_MODEL_VALUE_IS_WRITE_ONLY: '405',
    DATA_MODEL_VALUE_TYPE_MISMATCH: '406',
    DATA_MODEL_VALUE_OUT_OF_RANGE: '407'
};

// 日志函数
function scormLog(message, level = 'info') {
    if (SCORM_CONFIG.debug) {
        console.log(`[SCORM API ${level.toUpperCase()}] ${message}`);
    }
}

// HTTP请求函数
async function scormRequest(method, url, data = null) {
    try {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include' // 包含认证信息
        };

        if (data) {
            if (method === 'GET') {
                const params = new URLSearchParams(data);
                url += '?' + params.toString();
            } else {
                options.body = JSON.stringify(data);
            }
        }

        const response = await fetch(url, options);
        const result = await response.json();
        
        scormLog(`${method} ${url} -> ${response.status}`);
        return result;
    } catch (error) {
        scormLog(`请求失败: ${error.message}`, 'error');
        throw error;
    }
}

// 从URL获取包ID
function getPackageIdFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    const packageId = urlParams.get('packageId');
    if (packageId) {
        return parseInt(packageId);
    }
    
    // 从路径中提取包ID (例如: /storage/scorm/packages/4/files/...)
    const pathMatch = window.location.pathname.match(/\/packages\/(\d+)\//);
    if (pathMatch) {
        return parseInt(pathMatch[1]);
    }
    
    return null;
}

// SCORM 1.2 API实现
window.API = {
    // 初始化
    LMSInitialize: function(parameter) {
        scormLog('LMSInitialize called');
        
        if (scormState.initialized) {
            scormState.lastError = ERROR_CODES.ALREADY_INITIALIZED;
            scormState.errorString = 'Already initialized';
            return 'false';
        }

        if (scormState.terminated) {
            scormState.lastError = ERROR_CODES.CONTENT_INSTANCE_TERMINATED;
            scormState.errorString = 'Content instance terminated';
            return 'false';
        }

        try {
            scormState.packageId = getPackageIdFromUrl();
            if (!scormState.packageId) {
                scormState.lastError = ERROR_CODES.GENERAL_INITIALIZATION_FAILURE;
                scormState.errorString = 'Cannot determine package ID';
                return 'false';
            }

            // 异步初始化
            scormRequest('POST', `${SCORM_CONFIG.apiUrl}/packages/${scormState.packageId}/initialize`)
                .then(result => {
                    if (result.code === 200) {
                        scormState.attemptId = result.data.attempt_id;
                        scormState.initialized = true;
                        scormState.lastError = ERROR_CODES.NO_ERROR;
                        scormState.errorString = '';
                        scormLog(`初始化成功: attemptId=${scormState.attemptId}`);
                    } else {
                        scormLog(`初始化失败: ${result.msg}`, 'error');
                    }
                })
                .catch(error => {
                    scormLog(`初始化异常: ${error.message}`, 'error');
                });

            return 'true';
        } catch (error) {
            scormState.lastError = ERROR_CODES.GENERAL_INITIALIZATION_FAILURE;
            scormState.errorString = error.message;
            return 'false';
        }
    },

    // 获取值
    LMSGetValue: function(element) {
        scormLog(`LMSGetValue: ${element}`);
        
        if (!scormState.initialized) {
            scormState.lastError = ERROR_CODES.RETRIEVE_DATA_BEFORE_INITIALIZATION;
            scormState.errorString = 'Retrieve data before initialization';
            return '';
        }

        if (scormState.terminated) {
            scormState.lastError = ERROR_CODES.RETRIEVE_DATA_AFTER_TERMINATION;
            scormState.errorString = 'Retrieve data after termination';
            return '';
        }

        // 同步获取数据（实际应用中可能需要缓存）
        try {
            // 这里简化处理，实际应该从缓存或异步获取
            scormState.lastError = ERROR_CODES.NO_ERROR;
            scormState.errorString = '';
            
            // 返回默认值
            switch (element) {
                case 'cmi.core.lesson_status':
                    return 'not attempted';
                case 'cmi.core.score.raw':
                case 'cmi.core.score.max':
                case 'cmi.core.score.min':
                    return '';
                case 'cmi.core.total_time':
                case 'cmi.core.session_time':
                    return '00:00:00';
                case 'cmi.core.entry':
                    return 'ab-initio';
                case 'cmi.location':
                case 'cmi.suspend_data':
                    return '';
                default:
                    return '';
            }
        } catch (error) {
            scormState.lastError = ERROR_CODES.GENERAL_GET_FAILURE;
            scormState.errorString = error.message;
            return '';
        }
    },

    // 设置值
    LMSSetValue: function(element, value) {
        scormLog(`LMSSetValue: ${element} = ${value}`);
        
        if (!scormState.initialized) {
            scormState.lastError = ERROR_CODES.STORE_DATA_BEFORE_INITIALIZATION;
            scormState.errorString = 'Store data before initialization';
            return 'false';
        }

        if (scormState.terminated) {
            scormState.lastError = ERROR_CODES.STORE_DATA_AFTER_TERMINATION;
            scormState.errorString = 'Store data after termination';
            return 'false';
        }

        try {
            // 异步设置数据
            if (scormState.attemptId) {
                scormRequest('POST', `${SCORM_CONFIG.apiUrl}/attempts/${scormState.attemptId}/data`, {
                    element: element,
                    value: value
                }).then(result => {
                    if (result.code === 200) {
                        scormLog(`设置数据成功: ${element} = ${value}`);
                    } else {
                        scormLog(`设置数据失败: ${result.msg}`, 'error');
                    }
                }).catch(error => {
                    scormLog(`设置数据异常: ${error.message}`, 'error');
                });
            }

            scormState.lastError = ERROR_CODES.NO_ERROR;
            scormState.errorString = '';
            return 'true';
        } catch (error) {
            scormState.lastError = ERROR_CODES.GENERAL_SET_FAILURE;
            scormState.errorString = error.message;
            return 'false';
        }
    },

    // 提交数据
    LMSCommit: function(parameter) {
        scormLog('LMSCommit called');
        
        if (!scormState.initialized) {
            scormState.lastError = ERROR_CODES.COMMIT_BEFORE_INITIALIZATION;
            scormState.errorString = 'Commit before initialization';
            return 'false';
        }

        if (scormState.terminated) {
            scormState.lastError = ERROR_CODES.COMMIT_AFTER_TERMINATION;
            scormState.errorString = 'Commit after termination';
            return 'false';
        }

        try {
            // 异步提交数据
            if (scormState.attemptId) {
                scormRequest('POST', `${SCORM_CONFIG.apiUrl}/attempts/${scormState.attemptId}/commit`)
                    .then(result => {
                        if (result.code === 200) {
                            scormLog('数据提交成功');
                        } else {
                            scormLog(`数据提交失败: ${result.msg}`, 'error');
                        }
                    })
                    .catch(error => {
                        scormLog(`数据提交异常: ${error.message}`, 'error');
                    });
            }

            scormState.lastError = ERROR_CODES.NO_ERROR;
            scormState.errorString = '';
            return 'true';
        } catch (error) {
            scormState.lastError = ERROR_CODES.GENERAL_COMMIT_FAILURE;
            scormState.errorString = error.message;
            return 'false';
        }
    },

    // 结束
    LMSFinish: function(parameter) {
        scormLog('LMSFinish called');
        
        if (!scormState.initialized) {
            scormState.lastError = ERROR_CODES.TERMINATION_BEFORE_INITIALIZATION;
            scormState.errorString = 'Termination before initialization';
            return 'false';
        }

        if (scormState.terminated) {
            scormState.lastError = ERROR_CODES.TERMINATION_AFTER_TERMINATION;
            scormState.errorString = 'Termination after termination';
            return 'false';
        }

        try {
            // 异步终止会话
            if (scormState.attemptId) {
                scormRequest('POST', `${SCORM_CONFIG.apiUrl}/attempts/${scormState.attemptId}/terminate`)
                    .then(result => {
                        if (result.code === 200) {
                            scormLog('会话终止成功');
                        } else {
                            scormLog(`会话终止失败: ${result.msg}`, 'error');
                        }
                    })
                    .catch(error => {
                        scormLog(`会话终止异常: ${error.message}`, 'error');
                    });
            }

            scormState.terminated = true;
            scormState.lastError = ERROR_CODES.NO_ERROR;
            scormState.errorString = '';
            return 'true';
        } catch (error) {
            scormState.lastError = ERROR_CODES.GENERAL_TERMINATION_FAILURE;
            scormState.errorString = error.message;
            return 'false';
        }
    },

    // 获取最后错误
    LMSGetLastError: function() {
        return scormState.lastError;
    },

    // 获取错误字符串
    LMSGetErrorString: function(errorCode) {
        return scormState.errorString || 'No error';
    },

    // 获取诊断信息
    LMSGetDiagnostic: function(errorCode) {
        return scormState.errorString || 'No diagnostic information available';
    }
};

// SCORM 2004 API (API_1484_11)
window.API_1484_11 = {
    Initialize: window.API.LMSInitialize,
    GetValue: window.API.LMSGetValue,
    SetValue: window.API.LMSSetValue,
    Commit: window.API.LMSCommit,
    Terminate: window.API.LMSFinish,
    GetLastError: window.API.LMSGetLastError,
    GetErrorString: window.API.LMSGetErrorString,
    GetDiagnostic: window.API.LMSGetDiagnostic
};

// 页面卸载时自动终止会话
window.addEventListener('beforeunload', function() {
    if (scormState.initialized && !scormState.terminated) {
        window.API.LMSFinish('');
    }
});

scormLog('SCORM API适配器已加载');
