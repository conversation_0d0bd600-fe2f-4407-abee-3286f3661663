-- PlayEdu 数据初始化语句
-- 使用数据库: playedu

USE playedu;

-- 1. 插入迁移记录
INSERT INTO `migrations` (`migration`) VALUES
('20231208_14_00_00_migrations'),
('20231208_14_00_00_admin_permissions'),
('20231208_14_00_00_admin_logs'),
('20231208_14_00_00_admin_roles'),
('20231208_14_00_00_admin_role_permission'),
('20231208_14_00_00_admin_users'),
('20231208_14_00_00_admin_user_role'),
('20231208_14_00_00_app_config'),
('20231208_14_00_00_course_attachment'),
('20231208_14_00_00_course_chapters'),
('20231208_14_00_00_courses'),
('20231208_14_00_00_course_hours'),
('20231208_14_00_00_departments'),
('20231208_14_00_00_resource_categories'),
('20231208_14_00_00_resources'),
('20231208_14_00_00_resource_extra'),
('20231208_14_00_00_user_course_hour_records'),
('20231208_14_00_00_user_course_records'),
('20231208_14_00_00_user_department'),
('20231208_14_00_00_users'),
('20240322_17_29_17_ldap_user'),
('20240322_17_29_17_ldap_user_sync_detail'),
('20250805_13_00_00_create_scorm_packages'),
('20250805_14_00_00_create_scorm_attempts');

-- 2. 创建超级管理员角色
INSERT INTO `admin_roles` (`id`, `name`, `slug`, `created_at`, `updated_at`) VALUES
(1, '超级管理员', 'super-admin', NOW(), NOW());

-- 3. 创建超级管理员用户 (密码: 123456, salt: salt)
-- 密码哈希计算: MD5("123456" + "salt") = MD5("123456salt") = 4297f44b13955235245b2497399d7a93
INSERT INTO `admin_users` (`id`, `name`, `email`, `password`, `salt`, `login_ip`, `login_at`, `is_ban_login`, `login_times`, `created_at`, `updated_at`) VALUES
(1, '超级管理员', '<EMAIL>', '557086812aa0507392f2d40071edfc9e', 'salt', '', NULL, 0, 0, NOW(), NOW());

-- 4. 关联超级管理员用户和角色
INSERT INTO `admin_user_role` (`admin_id`, `role_id`) VALUES (1, 1);

-- 5. 插入管理员权限数据
INSERT INTO `admin_permissions` (`type`, `group_name`, `sort`, `name`, `slug`, `created_at`, `updated_at`) VALUES
-- 分类管理权限
('action', '分类管理', 0, '列表', 'resource-category-menu', NOW(), NOW()),
('action', '分类管理', 0, '新增|编辑|删除', 'resource-category', NOW(), NOW()),

-- 资源管理权限
('action', '资源管理', 0, '列表', 'resource-menu', NOW(), NOW()),
('action', '资源管理', 10, '资源上传', 'upload', NOW(), NOW()),

-- 学员管理权限
('action', '学员', 0, '列表', 'user-index', NOW(), NOW()),
('action', '学员', 5, '新增', 'user-store', NOW(), NOW()),
('action', '学员', 10, '编辑', 'user-update', NOW(), NOW()),
('action', '学员', 15, '删除', 'user-destroy', NOW(), NOW()),
('action', '学员', 20, '学习进度', 'user-learn', NOW(), NOW()),
('action', '学员', 25, '学习进度-删除', 'user-learn-destroy', NOW(), NOW()),

-- 部门管理权限
('action', '部门', 0, '新增|编辑|删除', 'department-cud', NOW(), NOW()),
('action', '部门', 10, '查看部门学员学习进度', 'department-user-learn', NOW(), NOW()),

-- 线上课权限
('action', '线上课', 0, '列表', 'course', NOW(), NOW()),
('action', '线上课', 5, '新增|编辑|删除', 'course-cud', NOW(), NOW()),
('action', '线上课', 10, '学员学习记录-列表', 'course-user', NOW(), NOW()),
('action', '线上课', 20, '学员学习记录-删除', 'course-user-destroy', NOW(), NOW()),

-- SCORM权限
('action', 'SCORM', 0, 'SCORM包管理', 'scorm-package', NOW(), NOW()),
('action', 'SCORM', 5, 'SCORM包新增|编辑|删除', 'scorm-package-cud', NOW(), NOW()),
('action', 'SCORM', 10, 'SCORM学习记录', 'scorm-attempt', NOW(), NOW()),

-- 直播权限
('action', '直播', 0, '直播会话管理', 'live-session', NOW(), NOW()),
('action', '直播', 5, '直播会话新增|编辑|删除', 'live-session-cud', NOW(), NOW()),

-- 系统管理权限
('action', '系统', 0, '系统配置', 'system-config', NOW(), NOW()),
('action', '系统', 10, '管理员日志', 'admin-log', NOW(), NOW()),
('action', '系统', 15, '管理员角色', 'admin-role', NOW(), NOW()),
('action', '系统', 20, '管理员-列表', 'admin-user-index', NOW(), NOW()),
('action', '系统', 25, '管理员-新增|编辑|删除', 'admin-user-cud', NOW(), NOW()),
('action', '系统', 30, '修改登录密码', 'password-change', NOW(), NOW()),
('action', '系统', 35, '缓存管理', 'cache-manage', NOW(), NOW()),

-- 数据权限
('data', '管理员', 0, '邮箱', 'data-admin-email', NOW(), NOW()),
('data', '学员', 0, '邮箱', 'data-user-email', NOW(), NOW()),
('data', '学员', 10, '姓名', 'data-user-name', NOW(), NOW());

-- 6. 为超级管理员分配所有权限
INSERT INTO `admin_role_permission` (`role_id`, `perm_id`)
SELECT 1, id FROM `admin_permissions`;

-- 7. 插入系统配置数据
INSERT INTO `app_config` (`group_name`, `name`, `sort`, `field_type`, `key_name`, `key_value`, `option_value`, `is_private`, `help`, `created_at`, `updated_at`) VALUES
-- 系统配置
('系统', '网站名', 0, 'input', 'app.name', 'PlayEdu', '', 0, '请输入网站名', NOW(), NOW()),
('系统', '网站地址', 10, 'input', 'app.url', '', '', 0, '请输入网站地址', NOW(), NOW()),
('系统', 'Logo', 20, 'image', 'app.logo', '', '', 0, '请上传网站Logo', NOW(), NOW()),
('系统', 'PC端Logo', 30, 'image', 'app.pc_logo', '', '', 0, '请上传PC端Logo', NOW(), NOW()),
('系统', 'H5端Logo', 40, 'image', 'app.h5_logo', '', '', 0, '请上传H5端Logo', NOW(), NOW()),
('系统', 'Favicon', 50, 'image', 'app.favicon', '', '', 0, '请上传网站Favicon', NOW(), NOW()),
('系统', '备案号', 60, 'input', 'app.icp', '', '', 0, '请输入备案号', NOW(), NOW()),

-- 学员配置
('学员', '学员默认头像', 0, 'image', 'member.default_avatar', '', '', 0, '请上传学员默认头像', NOW(), NOW()),
('学员', '启用手机号登录', 10, 'switch', 'member.enabled_mobile_login', '0', '', 0, '启用手机号登录', NOW(), NOW()),
('学员', '启用邮箱注册', 20, 'switch', 'member.enabled_email_register', '0', '', 0, '启用邮箱注册', NOW(), NOW()),
('学员', '启用手机号注册', 30, 'switch', 'member.enabled_mobile_register', '0', '', 0, '启用手机号注册', NOW(), NOW()),
('学员', '强制绑定手机号', 40, 'switch', 'member.enabled_mobile_bind_alert', '0', '', 0, '强制绑定手机号', NOW(), NOW()),
('学员', '学员注册协议', 50, 'textarea', 'member.protocol', '', '', 0, '学员注册协议', NOW(), NOW()),
('学员', '学员隐私协议', 60, 'textarea', 'member.private_protocol', '', '', 0, '学员隐私协议', NOW(), NOW()),

-- 其他配置
('其它', '播放跳转参数', 0, 'input', 'player.poster_params', '', '', 0, '播放跳转参数', NOW(), NOW()),
('其它', '播放器封面', 10, 'image', 'player.poster', '', '', 0, '播放器封面', NOW(), NOW()),
('其它', '播放器Logo', 20, 'image', 'player.logo', '', '', 0, '播放器Logo', NOW(), NOW()),
('其它', '播放器Logo跳转地址', 30, 'input', 'player.logo_url', '', '', 0, '播放器Logo跳转地址', NOW(), NOW()),

-- 支付配置
('支付', '启用支付功能', 0, 'switch', 'payment.enabled', '0', '', 0, '启用支付功能', NOW(), NOW()),
('支付', '支付成功跳转地址', 10, 'input', 'payment.success_url', '', '', 0, '支付成功跳转地址', NOW(), NOW()),

-- 短信配置
('短信', '短信服务商', 0, 'select', 'sms.default', 'aliyun', '{"aliyun":"阿里云","tencent":"腾讯云"}', 0, '短信服务商', NOW(), NOW()),
('短信', '阿里云AccessKeyId', 10, 'input', 'sms.aliyun.access_key_id', '', '', 1, '阿里云AccessKeyId', NOW(), NOW()),
('短信', '阿里云AccessKeySecret', 20, 'input', 'sms.aliyun.access_key_secret', '', '', 1, '阿里云AccessKeySecret', NOW(), NOW()),
('短信', '阿里云短信签名', 30, 'input', 'sms.aliyun.sign_name', '', '', 0, '阿里云短信签名', NOW(), NOW()),
('短信', '阿里云短信模板ID', 40, 'input', 'sms.aliyun.template_code', '', '', 0, '阿里云短信模板ID', NOW(), NOW()),

-- 邮件配置
('邮件', '邮件驱动', 0, 'select', 'mail.default', 'smtp', '{"smtp":"SMTP"}', 0, '邮件驱动', NOW(), NOW()),
('邮件', 'SMTP服务器', 10, 'input', 'mail.smtp.host', '', '', 0, 'SMTP服务器', NOW(), NOW()),
('邮件', 'SMTP端口', 20, 'input', 'mail.smtp.port', '587', '', 0, 'SMTP端口', NOW(), NOW()),
('邮件', 'SMTP用户名', 30, 'input', 'mail.smtp.username', '', '', 1, 'SMTP用户名', NOW(), NOW()),
('邮件', 'SMTP密码', 40, 'input', 'mail.smtp.password', '', '', 1, 'SMTP密码', NOW(), NOW()),
('邮件', 'SMTP加密方式', 50, 'select', 'mail.smtp.encryption', 'tls', '{"tls":"TLS","ssl":"SSL"}', 0, 'SMTP加密方式', NOW(), NOW()),
('邮件', '发件人邮箱', 60, 'input', 'mail.from.address', '', '', 0, '发件人邮箱', NOW(), NOW()),
('邮件', '发件人姓名', 70, 'input', 'mail.from.name', '', '', 0, '发件人姓名', NOW(), NOW()),

-- 存储配置
('存储', '默认存储', 0, 'select', 'filesystem.default', 'local', '{"local":"本地存储","s3":"S3存储","oss":"阿里云OSS","cos":"腾讯云COS"}', 0, '默认存储', NOW(), NOW()),
('存储', 'S3 Access Key', 10, 'input', 'filesystem.s3.access_key', '', '', 1, 'S3 Access Key', NOW(), NOW()),
('存储', 'S3 Secret Key', 20, 'input', 'filesystem.s3.secret_key', '', '', 1, 'S3 Secret Key', NOW(), NOW()),
('存储', 'S3 Bucket', 30, 'input', 'filesystem.s3.bucket', '', '', 0, 'S3 Bucket', NOW(), NOW()),
('存储', 'S3 Region', 40, 'input', 'filesystem.s3.region', '', '', 0, 'S3 Region', NOW(), NOW()),
('存储', 'S3 Endpoint', 50, 'input', 'filesystem.s3.endpoint', '', '', 0, 'S3 Endpoint', NOW(), NOW()),

-- LDAP配置
('LDAP', '启用LDAP', 0, 'switch', 'ldap.enabled', '0', '', 0, '启用LDAP', NOW(), NOW()),
('LDAP', 'LDAP服务器', 10, 'input', 'ldap.host', '', '', 0, 'LDAP服务器', NOW(), NOW()),
('LDAP', 'LDAP端口', 20, 'input', 'ldap.port', '389', '', 0, 'LDAP端口', NOW(), NOW()),
('LDAP', 'LDAP用户名', 30, 'input', 'ldap.username', '', '', 1, 'LDAP用户名', NOW(), NOW()),
('LDAP', 'LDAP密码', 40, 'input', 'ldap.password', '', '', 1, 'LDAP密码', NOW(), NOW()),
('LDAP', 'LDAP Base DN', 50, 'input', 'ldap.base_dn', '', '', 0, 'LDAP Base DN', NOW(), NOW()),
('LDAP', 'LDAP用户过滤器', 60, 'input', 'ldap.user_filter', '', '', 0, 'LDAP用户过滤器', NOW(), NOW());

-- 8. 插入资源分类数据（仿照OPPO企业培训）
INSERT INTO `resource_categories` (`id`, `parent_id`, `parent_chain`, `name`, `sort`, `created_at`, `updated_at`) VALUES
-- 一级分类
(1, 0, '', '产品培训', 10, NOW(), NOW()),
(2, 0, '', '销售技能', 20, NOW(), NOW()),
(3, 0, '', '客服技能', 30, NOW(), NOW()),
(4, 0, '', '企业文化', 40, NOW(), NOW()),
(5, 0, '', '管理培训', 50, NOW(), NOW()),
(6, 0, '', '新员工入职', 60, NOW(), NOW()),

-- 产品培训二级分类
(11, 1, '1', '手机产品', 10, NOW(), NOW()),
(12, 1, '1', '耳机产品', 20, NOW(), NOW()),
(13, 1, '1', '智能穿戴', 30, NOW(), NOW()),
(14, 1, '1', '生态产品', 40, NOW(), NOW()),

-- 手机产品三级分类
(111, 11, '1,11', 'Find系列', 10, NOW(), NOW()),
(112, 11, '1,11', 'Reno系列', 20, NOW(), NOW()),
(113, 11, '1,11', 'A系列', 30, NOW(), NOW()),
(114, 11, '1,11', 'K系列', 40, NOW(), NOW()),

-- 销售技能二级分类
(21, 2, '2', '销售话术', 10, NOW(), NOW()),
(22, 2, '2', '客户沟通', 20, NOW(), NOW()),
(23, 2, '2', '产品介绍', 30, NOW(), NOW()),
(24, 2, '2', '异议处理', 40, NOW(), NOW()),
(25, 2, '2', '成交技巧', 50, NOW(), NOW()),

-- 客服技能二级分类
(31, 3, '3', '服务礼仪', 10, NOW(), NOW()),
(32, 3, '3', '问题解答', 20, NOW(), NOW()),
(33, 3, '3', '投诉处理', 30, NOW(), NOW()),
(34, 3, '3', '售后服务', 40, NOW(), NOW()),

-- 企业文化二级分类
(41, 4, '4', '企业价值观', 10, NOW(), NOW()),
(42, 4, '4', '企业历史', 20, NOW(), NOW()),
(43, 4, '4', '品牌故事', 30, NOW(), NOW()),
(44, 4, '4', '行为准则', 40, NOW(), NOW()),

-- 管理培训二级分类
(51, 5, '5', '团队管理', 10, NOW(), NOW()),
(52, 5, '5', '绩效管理', 20, NOW(), NOW()),
(53, 5, '5', '沟通技巧', 30, NOW(), NOW()),
(54, 5, '5', '领导力', 40, NOW(), NOW()),

-- 新员工入职二级分类
(61, 6, '6', '公司介绍', 10, NOW(), NOW()),
(62, 6, '6', '制度规范', 20, NOW(), NOW()),
(63, 6, '6', '岗位职责', 30, NOW(), NOW()),
(64, 6, '6', '基础技能', 40, NOW(), NOW());

-- 9. 插入部门数据（仿照OPPO企业培训组织架构）
INSERT INTO `departments` (`id`, `name`, `parent_id`, `parent_chain`, `sort`, `created_at`, `updated_at`) VALUES
-- 一级部门
(1, 'OPPO集团', 0, '', 10, NOW(), NOW()),

-- 二级部门
(11, '销售部', 1, '1', 10, NOW(), NOW()),
(12, '客服部', 1, '1', 20, NOW(), NOW()),
(13, '产品部', 1, '1', 30, NOW(), NOW()),
(14, '市场部', 1, '1', 40, NOW(), NOW()),
(15, '人力资源部', 1, '1', 50, NOW(), NOW()),
(16, '培训部', 1, '1', 60, NOW(), NOW()),

-- 销售部三级部门
(111, '华北销售区', 11, '1,11', 10, NOW(), NOW()),
(112, '华东销售区', 11, '1,11', 20, NOW(), NOW()),
(113, '华南销售区', 11, '1,11', 30, NOW(), NOW()),
(114, '西南销售区', 11, '1,11', 40, NOW(), NOW()),
(115, '线上销售部', 11, '1,11', 50, NOW(), NOW()),

-- 客服部三级部门
(121, '售前客服组', 12, '1,12', 10, NOW(), NOW()),
(122, '售后客服组', 12, '1,12', 20, NOW(), NOW()),
(123, '技术支持组', 12, '1,12', 30, NOW(), NOW()),
(124, '投诉处理组', 12, '1,12', 40, NOW(), NOW()),

-- 产品部三级部门
(131, '手机产品组', 13, '1,13', 10, NOW(), NOW()),
(132, '配件产品组', 13, '1,13', 20, NOW(), NOW()),
(133, '生态产品组', 13, '1,13', 30, NOW(), NOW());

-- 10. 更新S3 MinIO存储配置
UPDATE `app_config` SET `key_value` = 's3' WHERE `key_name` = 'filesystem.default';
UPDATE `app_config` SET `key_value` = 'minioadmin' WHERE `key_name` = 'filesystem.s3.access_key';
UPDATE `app_config` SET `key_value` = 'Sbtr.123456' WHERE `key_name` = 'filesystem.s3.secret_key';
UPDATE `app_config` SET `key_value` = 'playedu' WHERE `key_name` = 'filesystem.s3.bucket';
UPDATE `app_config` SET `key_value` = 'us-east-1' WHERE `key_name` = 'filesystem.s3.region';
UPDATE `app_config` SET `key_value` = 'http://127.0.0.1:9876' WHERE `key_name` = 'filesystem.s3.endpoint';
