import React from "react";
import { Button } from "antd";
import { FileZipOutlined } from "@ant-design/icons";
import { UploadScormSub } from "./upload-scorm-sub";
import styles from "./index.module.less";

interface Props {
  onUpdate: () => void;
  text?: string;
  type?: "primary" | "default" | "dashed" | "link" | "text";
  size?: "large" | "middle" | "small";
}

export const UploadScormButton: React.FC<Props> = ({
  onUpdate,
  text = "上传SCORM",
  type = "primary",
  size = "middle",
}) => {
  const [showModal, setShowModal] = React.useState(false);

  return (
    <div className={styles["upload-scorm-button"]}>
      <Button
        type={type}
        size={size}
        icon={<FileZipOutlined />}
        onClick={() => setShowModal(true)}
      >
        {text}
      </Button>
      
      <UploadScormSub
        open={showModal}
        onUpdate={() => {
          onUpdate();
          setShowModal(false);
        }}
        onCancel={() => setShowModal(false)}
      />
    </div>
  );
};

export default UploadScormButton;
