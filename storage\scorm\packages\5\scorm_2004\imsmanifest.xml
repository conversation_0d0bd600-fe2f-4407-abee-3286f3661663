<?xml version="1.0" encoding="UTF-8"?>
<manifest identifier="SCORM_2004_Example" version="1.0"
          xmlns="http://www.imsglobal.org/xsd/imscp_v1p1"
          xmlns:adlcp="http://www.adlnet.org/xsd/adlcp_v1p3"
          xmlns:adlseq="http://www.adlnet.org/xsd/adlseq_v1p3"
          xmlns:adlnav="http://www.adlnet.org/xsd/adlnav_v1p3"
          xmlns:imsss="http://www.imsglobal.org/xsd/imsss"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://www.imsglobal.org/xsd/imscp_v1p1 imscp_v1p1.xsd
                              http://www.adlnet.org/xsd/adlcp_v1p3 adlcp_v1p3.xsd
                              http://www.adlnet.org/xsd/adlseq_v1p3 adlseq_v1p3.xsd
                              http://www.adlnet.org/xsd/adlnav_v1p3 adlnav_v1p3.xsd
                              http://www.imsglobal.org/xsd/imsss imsss_v1p0.xsd">

  <metadata>
    <schema>ADL SCORM</schema>
    <schemaversion>2004 4th Edition</schemaversion>
    <lom xmlns="http://ltsc.ieee.org/xsd/LOM"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://ltsc.ieee.org/xsd/LOM lomv1.0/lom.xsd">
      <general>
        <identifier>
          <catalog>URI</catalog>
          <entry>http://example.com/scorm2004/course001</entry>
        </identifier>
        <title>
          <string language="zh-CN">SCORM 2004 高级示例课程</string>
          <string language="en">SCORM 2004 Advanced Example Course</string>
        </title>
        <description>
          <string language="zh-CN">这是一个完整的SCORM 2004标准课件示例，展示了高级排序和导航功能，包含自适应学习路径和复杂的交互内容。</string>
          <string language="en">This is a complete SCORM 2004 standard courseware example showcasing advanced sequencing and navigation features with adaptive learning paths and complex interactive content.</string>
        </description>
        <keyword>
          <string language="zh-CN">SCORM 2004, 自适应学习, 排序规则, 导航控制</string>
          <string language="en">SCORM 2004, adaptive learning, sequencing rules, navigation control</string>
        </keyword>
        <language>zh-CN</language>
        <language>en</language>
        <structure>
          <source>LOMv1.0</source>
          <value>hierarchical</value>
        </structure>
        <aggregationLevel>
          <source>LOMv1.0</source>
          <value>2</value>
        </aggregationLevel>
      </general>
      <lifeCycle>
        <version>
          <string language="en">2.0</string>
        </version>
        <status>
          <source>LOMv1.0</source>
          <value>final</value>
        </status>
        <contribute>
          <role>
            <source>LOMv1.0</source>
            <value>author</value>
          </role>
          <entity>高级课程开发团队</entity>
          <date>
            <dateTime>2024-01-01</dateTime>
          </date>
        </contribute>
      </lifeCycle>
      <technical>
        <format>text/html</format>
        <format>application/javascript</format>
        <format>text/css</format>
        <format>image/jpeg</format>
        <format>image/png</format>
        <format>video/mp4</format>
        <format>audio/mp3</format>
        <size>52428800</size>
        <location>http://example.com/scorm2004/course001</location>
        <requirement>
          <orComposite>
            <type>
              <source>LOMv1.0</source>
              <value>browser</value>
            </type>
            <name>
              <source>LOMv1.0</source>
              <value>any</value>
            </name>
            <minimumVersion>HTML5</minimumVersion>
            <maximumVersion>HTML5</maximumVersion>
          </orComposite>
        </requirement>
        <installationRemarks>
          <string language="zh-CN">需要支持HTML5和JavaScript的现代浏览器</string>
        </installationRemarks>
        <otherPlatformRequirements>
          <string language="zh-CN">建议使用Chrome、Firefox、Safari或Edge浏览器</string>
        </otherPlatformRequirements>
        <duration>
          <duration>PT3H30M</duration>
        </duration>
      </technical>
      <educational>
        <interactivityType>
          <source>LOMv1.0</source>
          <value>mixed</value>
        </interactivityType>
        <learningResourceType>
          <source>LOMv1.0</source>
          <value>exercise</value>
        </learningResourceType>
        <learningResourceType>
          <source>LOMv1.0</source>
          <value>simulation</value>
        </learningResourceType>
        <interactivityLevel>
          <source>LOMv1.0</source>
          <value>high</value>
        </interactivityLevel>
        <semanticDensity>
          <source>LOMv1.0</source>
          <value>medium</value>
        </semanticDensity>
        <intendedEndUserRole>
          <source>LOMv1.0</source>
          <value>learner</value>
        </intendedEndUserRole>
        <context>
          <source>LOMv1.0</source>
          <value>training</value>
        </context>
        <typicalAgeRange>
          <string language="en">18-65</string>
        </typicalAgeRange>
        <difficulty>
          <source>LOMv1.0</source>
          <value>medium</value>
        </difficulty>
        <typicalLearningTime>
          <duration>PT3H30M</duration>
        </typicalLearningTime>
        <description>
          <string language="zh-CN">本高级课程采用自适应学习技术，根据学习者的表现动态调整学习路径，适合有一定基础的学习者深入学习。</string>
        </description>
        <language>zh-CN</language>
      </educational>
      <rights>
        <cost>
          <source>LOMv1.0</source>
          <value>no</value>
        </cost>
        <copyrightAndOtherRestrictions>
          <source>LOMv1.0</source>
          <value>yes</value>
        </copyrightAndOtherRestrictions>
        <description>
          <string language="zh-CN">版权所有 © 2024 高级课程开发团队。保留所有权利。未经授权不得复制或分发。</string>
        </description>
      </rights>
    </lom>
  </metadata>

  <organizations default="default_org">
    <organization identifier="default_org" adlseq:objectivesGlobalToSystem="false">
      <title>SCORM 2004 高级课程结构</title>
      
      <!-- 全局排序规则 -->
      <imsss:sequencing>
        <imsss:controlMode choice="true" choiceExit="true" flow="true" forwardOnly="false"/>
        <imsss:limitConditions attemptLimit="3" attemptAbsoluteDurationLimit="PT4H"/>
        <imsss:rollupRules rollupObjectiveSatisfied="true" rollupProgressCompletion="true">
          <imsss:rollupRule childActivitySet="all">
            <imsss:rollupConditions conditionCombination="any">
              <imsss:rollupCondition condition="satisfied"/>
            </imsss:rollupConditions>
            <imsss:rollupAction action="satisfied"/>
          </imsss:rollupRule>
        </imsss:rollupRules>
        <imsss:objectives>
          <imsss:primaryObjective objectiveID="course_objective" satisfiedByMeasure="true">
            <imsss:minNormalizedMeasure>0.8</imsss:minNormalizedMeasure>
          </imsss:primaryObjective>
        </imsss:objectives>
      </imsss:sequencing>

      <!-- 第一单元：基础学习 -->
      <item identifier="unit1" identifierref="unit1_resource">
        <title>第一单元：基础概念与理论</title>
        <adlcp:timeLimitAction>exit,message</adlcp:timeLimitAction>
        <adlcp:dataFromLMS>基础单元数据</adlcp:dataFromLMS>
        <adlcp:completionThreshold>0.8</adlcp:completionThreshold>
        
        <imsss:sequencing>
          <imsss:controlMode choice="true" choiceExit="true" flow="true" forwardOnly="false"/>
          <imsss:limitConditions attemptLimit="2" attemptAbsoluteDurationLimit="PT1H"/>
          <imsss:preConditionRule>
            <imsss:ruleConditions conditionCombination="any">
              <imsss:ruleCondition condition="always"/>
            </imsss:ruleConditions>
            <imsss:ruleAction action="skip"/>
          </imsss:preConditionRule>
          <imsss:objectives>
            <imsss:primaryObjective objectiveID="unit1_objective" satisfiedByMeasure="true">
              <imsss:minNormalizedMeasure>0.75</imsss:minNormalizedMeasure>
            </imsss:primaryObjective>
          </imsss:objectives>
        </imsss:sequencing>

        <!-- 子活动 -->
        <item identifier="lesson1_1" identifierref="lesson1_1_resource">
          <title>1.1 基础理论介绍</title>
          <adlcp:masteryscore>80</adlcp:masteryscore>
          <imsss:sequencing>
            <imsss:controlMode choice="true" choiceExit="true" flow="true"/>
            <imsss:objectives>
              <imsss:primaryObjective objectiveID="lesson1_1_obj" satisfiedByMeasure="true">
                <imsss:minNormalizedMeasure>0.8</imsss:minNormalizedMeasure>
              </imsss:primaryObjective>
            </imsss:objectives>
          </imsss:sequencing>
        </item>

        <item identifier="lesson1_2" identifierref="lesson1_2_resource">
          <title>1.2 核心概念解析</title>
          <adlcp:masteryscore>85</adlcp:masteryscore>
          <imsss:sequencing>
            <imsss:controlMode choice="true" choiceExit="true" flow="true"/>
            <imsss:preConditionRule>
              <imsss:ruleConditions conditionCombination="all">
                <imsss:ruleCondition referencedObjective="lesson1_1_obj" condition="satisfied"/>
              </imsss:ruleConditions>
              <imsss:ruleAction action="skip"/>
            </imsss:preConditionRule>
            <imsss:objectives>
              <imsss:primaryObjective objectiveID="lesson1_2_obj" satisfiedByMeasure="true">
                <imsss:minNormalizedMeasure>0.85</imsss:minNormalizedMeasure>
              </imsss:primaryObjective>
            </imsss:objectives>
          </imsss:sequencing>
        </item>

        <item identifier="assessment1" identifierref="assessment1_resource">
          <title>第一单元测试</title>
          <adlcp:masteryscore>90</adlcp:masteryscore>
          <imsss:sequencing>
            <imsss:controlMode choice="true" choiceExit="true" flow="true"/>
            <imsss:limitConditions attemptLimit="3"/>
            <imsss:preConditionRule>
              <imsss:ruleConditions conditionCombination="all">
                <imsss:ruleCondition referencedObjective="lesson1_1_obj" condition="satisfied"/>
                <imsss:ruleCondition referencedObjective="lesson1_2_obj" condition="satisfied"/>
              </imsss:ruleConditions>
              <imsss:ruleAction action="skip"/>
            </imsss:preConditionRule>
            <imsss:objectives>
              <imsss:primaryObjective objectiveID="assessment1_obj" satisfiedByMeasure="true">
                <imsss:minNormalizedMeasure>0.9</imsss:minNormalizedMeasure>
              </imsss:primaryObjective>
            </imsss:objectives>
          </imsss:sequencing>
        </item>
      </item>

      <!-- 第二单元：进阶学习 -->
      <item identifier="unit2" identifierref="unit2_resource">
        <title>第二单元：进阶应用与实践</title>
        <adlcp:timeLimitAction>continue,message</adlcp:timeLimitAction>
        <adlcp:completionThreshold>0.85</adlcp:completionThreshold>
        
        <imsss:sequencing>
          <imsss:controlMode choice="true" choiceExit="true" flow="true" forwardOnly="false"/>
          <imsss:limitConditions attemptLimit="2" attemptAbsoluteDurationLimit="PT1H30M"/>
          <imsss:preConditionRule>
            <imsss:ruleConditions conditionCombination="all">
              <imsss:ruleCondition referencedObjective="unit1_objective" condition="satisfied"/>
            </imsss:ruleConditions>
            <imsss:ruleAction action="skip"/>
          </imsss:preConditionRule>
          <imsss:objectives>
            <imsss:primaryObjective objectiveID="unit2_objective" satisfiedByMeasure="true">
              <imsss:minNormalizedMeasure>0.85</imsss:minNormalizedMeasure>
            </imsss:primaryObjective>
          </imsss:objectives>
        </imsss:sequencing>

        <item identifier="lesson2_1" identifierref="lesson2_1_resource">
          <title>2.1 高级技术应用</title>
          <adlcp:masteryscore>85</adlcp:masteryscore>
        </item>

        <item identifier="lesson2_2" identifierref="lesson2_2_resource">
          <title>2.2 实践案例分析</title>
          <adlcp:masteryscore>88</adlcp:masteryscore>
        </item>

        <item identifier="simulation1" identifierref="simulation1_resource">
          <title>交互式模拟练习</title>
          <adlcp:masteryscore>90</adlcp:masteryscore>
        </item>
      </item>

      <!-- 第三单元：综合评估 -->
      <item identifier="unit3" identifierref="unit3_resource">
        <title>第三单元：综合评估与认证</title>
        <adlcp:timeLimitAction>exit,message</adlcp:timeLimitAction>
        <adlcp:completionThreshold>0.95</adlcp:completionThreshold>
        
        <imsss:sequencing>
          <imsss:controlMode choice="true" choiceExit="true" flow="true" forwardOnly="true"/>
          <imsss:limitConditions attemptLimit="1" attemptAbsoluteDurationLimit="PT2H"/>
          <imsss:preConditionRule>
            <imsss:ruleConditions conditionCombination="all">
              <imsss:ruleCondition referencedObjective="unit1_objective" condition="satisfied"/>
              <imsss:ruleCondition referencedObjective="unit2_objective" condition="satisfied"/>
            </imsss:ruleConditions>
            <imsss:ruleAction action="skip"/>
          </imsss:preConditionRule>
          <imsss:objectives>
            <imsss:primaryObjective objectiveID="final_assessment_obj" satisfiedByMeasure="true">
              <imsss:minNormalizedMeasure>0.95</imsss:minNormalizedMeasure>
            </imsss:primaryObjective>
          </imsss:objectives>
        </imsss:sequencing>

        <item identifier="final_exam" identifierref="final_exam_resource">
          <title>综合考试</title>
          <adlcp:masteryscore>95</adlcp:masteryscore>
        </item>

        <item identifier="certification" identifierref="certification_resource">
          <title>认证颁发</title>
          <imsss:sequencing>
            <imsss:preConditionRule>
              <imsss:ruleConditions conditionCombination="all">
                <imsss:ruleCondition referencedObjective="final_assessment_obj" condition="satisfied"/>
              </imsss:ruleConditions>
              <imsss:ruleAction action="skip"/>
            </imsss:preConditionRule>
          </imsss:sequencing>
        </item>
      </item>
    </organization>
  </organizations>

  <resources>
    <!-- 第一单元资源 -->
    <resource identifier="unit1_resource" type="webcontent" adlcp:scormType="sco" href="content/unit1/index.html">
      <file href="content/unit1/index.html"/>
      <file href="content/unit1/style.css"/>
      <file href="content/unit1/script.js"/>
      <file href="shared/scorm2004_api_wrapper.js"/>
      <file href="shared/common.css"/>
      <dependency identifierref="common_files"/>
    </resource>

    <resource identifier="lesson1_1_resource" type="webcontent" adlcp:scormType="sco" href="content/unit1/lesson1.html">
      <file href="content/unit1/lesson1.html"/>
      <file href="content/unit1/lesson1.js"/>
      <file href="shared/scorm2004_api_wrapper.js"/>
      <dependency identifierref="common_files"/>
    </resource>

    <resource identifier="lesson1_2_resource" type="webcontent" adlcp:scormType="sco" href="content/unit1/lesson2.html">
      <file href="content/unit1/lesson2.html"/>
      <file href="content/unit1/lesson2.js"/>
      <file href="shared/scorm2004_api_wrapper.js"/>
      <dependency identifierref="common_files"/>
    </resource>

    <resource identifier="assessment1_resource" type="webcontent" adlcp:scormType="sco" href="content/unit1/assessment.html">
      <file href="content/unit1/assessment.html"/>
      <file href="content/unit1/assessment.js"/>
      <file href="shared/scorm2004_api_wrapper.js"/>
      <dependency identifierref="common_files"/>
    </resource>

    <!-- 第二单元资源 -->
    <resource identifier="unit2_resource" type="webcontent" adlcp:scormType="sco" href="content/unit2/index.html">
      <file href="content/unit2/index.html"/>
      <file href="content/unit2/style.css"/>
      <file href="content/unit2/script.js"/>
      <file href="shared/scorm2004_api_wrapper.js"/>
      <dependency identifierref="common_files"/>
    </resource>

    <resource identifier="lesson2_1_resource" type="webcontent" adlcp:scormType="sco" href="content/unit2/lesson1.html">
      <file href="content/unit2/lesson1.html"/>
      <file href="content/unit2/lesson1.js"/>
      <file href="shared/scorm2004_api_wrapper.js"/>
      <dependency identifierref="common_files"/>
    </resource>

    <resource identifier="lesson2_2_resource" type="webcontent" adlcp:scormType="sco" href="content/unit2/lesson2.html">
      <file href="content/unit2/lesson2.html"/>
      <file href="content/unit2/lesson2.js"/>
      <file href="shared/scorm2004_api_wrapper.js"/>
      <dependency identifierref="common_files"/>
    </resource>

    <resource identifier="simulation1_resource" type="webcontent" adlcp:scormType="sco" href="content/unit2/simulation.html">
      <file href="content/unit2/simulation.html"/>
      <file href="content/unit2/simulation.js"/>
      <file href="shared/scorm2004_api_wrapper.js"/>
      <dependency identifierref="common_files"/>
    </resource>

    <!-- 第三单元资源 -->
    <resource identifier="unit3_resource" type="webcontent" adlcp:scormType="sco" href="content/unit3/index.html">
      <file href="content/unit3/index.html"/>
      <file href="content/unit3/style.css"/>
      <file href="content/unit3/script.js"/>
      <file href="shared/scorm2004_api_wrapper.js"/>
      <dependency identifierref="common_files"/>
    </resource>

    <resource identifier="final_exam_resource" type="webcontent" adlcp:scormType="sco" href="content/unit3/final_exam.html">
      <file href="content/unit3/final_exam.html"/>
      <file href="content/unit3/final_exam.js"/>
      <file href="shared/scorm2004_api_wrapper.js"/>
      <dependency identifierref="common_files"/>
    </resource>

    <resource identifier="certification_resource" type="webcontent" adlcp:scormType="sco" href="content/unit3/certification.html">
      <file href="content/unit3/certification.html"/>
      <file href="content/unit3/certification.js"/>
      <file href="shared/scorm2004_api_wrapper.js"/>
      <dependency identifierref="common_files"/>
    </resource>

    <!-- 公共资源 -->
    <resource identifier="common_files" type="webcontent">
      <file href="shared/jquery.min.js"/>
      <file href="shared/bootstrap.min.css"/>
      <file href="shared/bootstrap.min.js"/>
      <file href="shared/fontawesome.min.css"/>
      <file href="shared/chart.min.js"/>
      <file href="images/logo.png"/>
      <file href="images/background.jpg"/>
      <file href="media/intro_video.mp4"/>
      <file href="media/background_music.mp3"/>
    </resource>
  </resources>
</manifest>
