.scorm-player {
  width: 100%;
  height: 100%;
  position: relative;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 16px;

    .ant-spin {
      font-size: 24px;
    }

    div {
      font-size: 16px;
      color: #666;
    }
  }

  .scorm-iframe {
    width: 100%;
    height: calc(100% - 60px); // 为进度条留出空间
    border: none;
    background: #fff;
  }
}

.fullscreen-modal {
  .ant-modal {
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .ant-modal-content {
    height: 100vh !important;
  }

  .ant-modal-body {
    height: 100vh !important;
  }
}

.scorm-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  
  .control-button {
    margin-left: 8px;
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white;
    
    &:hover {
      background: rgba(0, 0, 0, 0.9);
      color: white;
    }
  }
}

.scorm-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-top: 1px solid #e8e8e8;
  
  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #666;
    
    .progress-text {
      display: flex;
      gap: 16px;
    }
    
    .progress-bar {
      flex: 1;
      margin: 0 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .scorm-player {
    .scorm-controls {
      top: 5px;
      right: 5px;
      
      .control-button {
        margin-left: 4px;
        padding: 4px 8px;
        font-size: 12px;
      }
    }
    
    .scorm-progress {
      padding: 4px 8px;
      
      .progress-info {
        flex-direction: column;
        gap: 4px;
        
        .progress-text {
          gap: 8px;
        }
        
        .progress-bar {
          margin: 4px 0;
        }
      }
    }
  }
}
