package xyz.playedu.api.controller.frontend;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import xyz.playedu.common.exception.ServiceException;
import xyz.playedu.course.domain.ScormPackage;
import xyz.playedu.course.service.ScormPackageService;

import jakarta.servlet.http.HttpServletRequest;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@RestController
@RequestMapping("/storage/scorm")
@Slf4j
public class ScormFileController {

    @Autowired
    private ScormPackageService scormPackageService;

    /**
     * 公开访问SCORM包中的文件（不需要认证）
     */
    @GetMapping("/packages/{id}/files/**")
    public ResponseEntity<ByteArrayResource> getScormFile(
            @PathVariable Integer id,
            HttpServletRequest request) throws ServiceException {
        
        // 获取SCORM包信息
        ScormPackage scormPackage = scormPackageService.getById(id);
        if (scormPackage == null) {
            throw new ServiceException("SCORM包不存在");
        }

        if (scormPackage.getParseStatus() != 1) {
            throw new ServiceException("SCORM包尚未解析完成");
        }

        // 获取请求的文件路径
        String requestURI = request.getRequestURI();
        String filePath = requestURI.substring(requestURI.indexOf("/files/") + 7);
        
        try {
            // 构建完整的文件路径
            String extractPath = scormPackage.getExtractPath();
            Path fullPath = Paths.get(extractPath, filePath);
            
            // 检查文件是否存在
            if (!Files.exists(fullPath)) {
                throw new ServiceException("文件不存在: " + filePath);
            }

            // 读取文件内容
            byte[] fileContent = Files.readAllBytes(fullPath);

            // 确定文件类型
            String fileName = fullPath.getFileName().toString();
            String contentType = getContentType(fileName);

            // 如果是HTML文件，注入SCORM API
            if (contentType.equals("text/html")) {
                String htmlContent = new String(fileContent, "UTF-8");
                htmlContent = injectScormApi(htmlContent, id);
                fileContent = htmlContent.getBytes("UTF-8");
            }

            // 返回文件内容
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + fileName + "\"")
                    .header(HttpHeaders.CACHE_CONTROL, "public, max-age=3600")
                    .body(new ByteArrayResource(fileContent));
                    
        } catch (Exception e) {
            log.error("读取SCORM文件失败: {}", filePath, e);
            throw new ServiceException("读取文件失败: " + e.getMessage());
        }
    }

    /**
     * 根据文件扩展名确定Content-Type
     */
    private String getContentType(String fileName) {
        String extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
        switch (extension) {
            case "html":
            case "htm":
                return "text/html";
            case "css":
                return "text/css";
            case "js":
                return "application/javascript";
            case "json":
                return "application/json";
            case "xml":
                return "application/xml";
            case "png":
                return "image/png";
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "gif":
                return "image/gif";
            case "svg":
                return "image/svg+xml";
            case "pdf":
                return "application/pdf";
            case "mp4":
                return "video/mp4";
            case "mp3":
                return "audio/mpeg";
            case "woff":
            case "woff2":
                return "font/woff";
            case "ttf":
                return "font/ttf";
            case "eot":
                return "application/vnd.ms-fontobject";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 在HTML文件中注入SCORM API
     */
    private String injectScormApi(String htmlContent, Integer packageId) {
        // 内联SCORM API脚本
        String scormApiScript = String.format(
            "<script>\n" +
            "// SCORM API配置\n" +
            "window.SCORM_PACKAGE_ID = %d;\n" +
            "window.SCORM_API_BASE_URL = 'http://localhost:9898/api/v1/scorm';\n" +
            "\n" +
            "// 内联SCORM API实现\n" +
            "%s\n" +
            "</script>\n",
            packageId,
            getInlineScormApiScript()
        );

        // 在</head>标签前插入API脚本
        if (htmlContent.contains("</head>")) {
            htmlContent = htmlContent.replace("</head>", scormApiScript + "</head>");
        } else if (htmlContent.contains("<html>")) {
            // 如果没有head标签，在html标签后插入
            htmlContent = htmlContent.replace("<html>", "<html>\n<head>\n" + scormApiScript + "</head>");
        } else {
            // 如果都没有，在文档开头插入
            htmlContent = scormApiScript + "\n" + htmlContent;
        }

        log.debug("已为SCORM包 {} 注入API脚本", packageId);
        return htmlContent;
    }

    /**
     * 获取内联SCORM API脚本
     */
    private String getInlineScormApiScript() {
        return "// SCORM API状态\\n" +
               "let scormState = {\\n" +
               "    initialized: false,\\n" +
               "    terminated: false,\\n" +
               "    packageId: window.SCORM_PACKAGE_ID,\\n" +
               "    attemptId: null,\\n" +
               "    lastError: '0',\\n" +
               "    errorString: ''\\n" +
               "};\\n" +
               "\\n" +
               "// HTTP请求函数\\n" +
               "async function scormRequest(method, url, data = null) {\\n" +
               "    try {\\n" +
               "        const options = {\\n" +
               "            method: method,\\n" +
               "            headers: { 'Content-Type': 'application/json' },\\n" +
               "            credentials: 'include'\\n" +
               "        };\\n" +
               "        if (data) {\\n" +
               "            if (method === 'GET') {\\n" +
               "                const params = new URLSearchParams(data);\\n" +
               "                url += '?' + params.toString();\\n" +
               "            } else {\\n" +
               "                options.body = JSON.stringify(data);\\n" +
               "            }\\n" +
               "        }\\n" +
               "        const response = await fetch(url, options);\\n" +
               "        return await response.json();\\n" +
               "    } catch (error) {\\n" +
               "        console.error('SCORM API请求失败:', error);\\n" +
               "        throw error;\\n" +
               "    }\\n" +
               "}\\n" +
               "\\n" +
               "// SCORM 1.2 API\\n" +
               "window.API = {\\n" +
               "    LMSInitialize: function(parameter) {\\n" +
               "        console.log('SCORM: LMSInitialize called');\\n" +
               "        if (scormState.initialized) {\\n" +
               "            scormState.lastError = '103';\\n" +
               "            return 'false';\\n" +
               "        }\\n" +
               "        scormRequest('POST', window.SCORM_API_BASE_URL + '/packages/' + scormState.packageId + '/initialize')\\n" +
               "            .then(result => {\\n" +
               "                if (result.code === 200) {\\n" +
               "                    scormState.attemptId = result.data.attempt_id;\\n" +
               "                    scormState.initialized = true;\\n" +
               "                    scormState.lastError = '0';\\n" +
               "                    console.log('SCORM: 初始化成功, attemptId=' + scormState.attemptId);\\n" +
               "                }\\n" +
               "            })\\n" +
               "            .catch(error => console.error('SCORM: 初始化失败', error));\\n" +
               "        return 'true';\\n" +
               "    },\\n" +
               "    LMSGetValue: function(element) {\\n" +
               "        console.log('SCORM: LMSGetValue(' + element + ')');\\n" +
               "        if (!scormState.initialized) {\\n" +
               "            scormState.lastError = '122';\\n" +
               "            return '';\\n" +
               "        }\\n" +
               "        // 返回默认值\\n" +
               "        switch (element) {\\n" +
               "            case 'cmi.core.lesson_status': return 'not attempted';\\n" +
               "            case 'cmi.core.entry': return 'ab-initio';\\n" +
               "            case 'cmi.core.total_time': return '00:00:00';\\n" +
               "            case 'cmi.core.session_time': return '00:00:00';\\n" +
               "            default: return '';\\n" +
               "        }\\n" +
               "    },\\n" +
               "    LMSSetValue: function(element, value) {\\n" +
               "        console.log('SCORM: LMSSetValue(' + element + ', ' + value + ')');\\n" +
               "        if (!scormState.initialized) {\\n" +
               "            scormState.lastError = '132';\\n" +
               "            return 'false';\\n" +
               "        }\\n" +
               "        if (scormState.attemptId) {\\n" +
               "            scormRequest('POST', window.SCORM_API_BASE_URL + '/attempts/' + scormState.attemptId + '/data', {\\n" +
               "                element: element,\\n" +
               "                value: value\\n" +
               "            }).catch(error => console.error('SCORM: 设置数据失败', error));\\n" +
               "        }\\n" +
               "        scormState.lastError = '0';\\n" +
               "        return 'true';\\n" +
               "    },\\n" +
               "    LMSCommit: function(parameter) {\\n" +
               "        console.log('SCORM: LMSCommit called');\\n" +
               "        if (!scormState.initialized) {\\n" +
               "            scormState.lastError = '142';\\n" +
               "            return 'false';\\n" +
               "        }\\n" +
               "        if (scormState.attemptId) {\\n" +
               "            scormRequest('POST', window.SCORM_API_BASE_URL + '/attempts/' + scormState.attemptId + '/commit')\\n" +
               "                .catch(error => console.error('SCORM: 提交数据失败', error));\\n" +
               "        }\\n" +
               "        scormState.lastError = '0';\\n" +
               "        return 'true';\\n" +
               "    },\\n" +
               "    LMSFinish: function(parameter) {\\n" +
               "        console.log('SCORM: LMSFinish called');\\n" +
               "        if (!scormState.initialized) {\\n" +
               "            scormState.lastError = '112';\\n" +
               "            return 'false';\\n" +
               "        }\\n" +
               "        if (scormState.attemptId) {\\n" +
               "            scormRequest('POST', window.SCORM_API_BASE_URL + '/attempts/' + scormState.attemptId + '/terminate')\\n" +
               "                .catch(error => console.error('SCORM: 终止会话失败', error));\\n" +
               "        }\\n" +
               "        scormState.terminated = true;\\n" +
               "        scormState.lastError = '0';\\n" +
               "        return 'true';\\n" +
               "    },\\n" +
               "    LMSGetLastError: function() { return scormState.lastError; },\\n" +
               "    LMSGetErrorString: function(errorCode) { return scormState.errorString || 'No error'; },\\n" +
               "    LMSGetDiagnostic: function(errorCode) { return scormState.errorString || 'No diagnostic'; }\\n" +
               "};\\n" +
               "\\n" +
               "// SCORM 2004 API\\n" +
               "window.API_1484_11 = {\\n" +
               "    Initialize: window.API.LMSInitialize,\\n" +
               "    GetValue: window.API.LMSGetValue,\\n" +
               "    SetValue: window.API.LMSSetValue,\\n" +
               "    Commit: window.API.LMSCommit,\\n" +
               "    Terminate: window.API.LMSFinish,\\n" +
               "    GetLastError: window.API.LMSGetLastError,\\n" +
               "    GetErrorString: window.API.LMSGetErrorString,\\n" +
               "    GetDiagnostic: window.API.LMSGetDiagnostic\\n" +
               "};\\n" +
               "\\n" +
               "// 页面卸载时自动终止\\n" +
               "window.addEventListener('beforeunload', function() {\\n" +
               "    if (scormState.initialized && !scormState.terminated) {\\n" +
               "        window.API.LMSFinish('');\\n" +
               "    }\\n" +
               "});\\n" +
               "\\n" +
               "console.log('SCORM API已注入并可用');";
    }
}
