<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.course.mapper.CourseCategoryMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.course.domain.CourseCategory">
            <result property="courseId" column="course_id" jdbcType="INTEGER"/>
            <result property="categoryId" column="category_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        course_id,category_id
    </sql>
</mapper>
