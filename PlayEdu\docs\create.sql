-- PlayEdu 数据库建表语句
-- 使用数据库: playedu

USE playedu;

-- 1. 迁移记录表
CREATE TABLE `migrations` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `migration` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '变更记录',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='迁移记录表';

-- 2. 管理员权限表
CREATE TABLE `admin_permissions` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类型[行为:action,数据:data]',
  `group_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分组',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '升序',
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '权限名',
  `slug` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'slug',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员权限表';

-- 3. 管理员日志表
CREATE TABLE `admin_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `admin_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '管理员姓名',
  `module` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '模块',
  `title` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求方法标题',
  `opt` int(2) NOT NULL DEFAULT 0 COMMENT '操作指令（0其它 1新增 2修改 3删除 4登录 5退出登录）',
  `request_method` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求方法',
  `request_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求地址',
  `request_ip` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求IP',
  `request_ua` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求UA',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'url',
  `method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'method',
  `request_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '请求数据',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员日志表';

-- 4. 管理员角色表
CREATE TABLE `admin_roles` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名',
  `slug` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'slug',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员角色表';

-- 5. 管理员角色权限关联表
CREATE TABLE `admin_role_permission` (
  `role_id` int(10) unsigned NOT NULL COMMENT '角色ID',
  `perm_id` int(10) unsigned NOT NULL COMMENT '权限ID',
  PRIMARY KEY (`role_id`,`perm_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员角色权限关联表';

-- 6. 管理员表
CREATE TABLE `admin_users` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `email` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `password` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `salt` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'salt',
  `login_ip` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '登录IP',
  `login_at` timestamp NULL DEFAULT NULL COMMENT '登录时间',
  `is_ban_login` tinyint(4) NOT NULL DEFAULT 0 COMMENT '禁止登录[1:是,0:否]',
  `login_times` int(11) NOT NULL DEFAULT 0 COMMENT '登录次数',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `admin_users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 7. 管理员角色关联表
CREATE TABLE `admin_user_role` (
  `admin_id` int(10) unsigned NOT NULL COMMENT '管理员ID',
  `role_id` int(10) unsigned NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`admin_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员角色关联表';

-- 8. 系统配置表
CREATE TABLE `app_config` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `group_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分组',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '配置名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '升序',
  `field_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '表单类型',
  `key_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置key',
  `key_value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '配置value',
  `option_value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '可选值',
  `is_private` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否私有配置',
  `is_hidden` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否隐藏配置',
  `help` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '帮助信息',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `app_config_key_name_unique` (`key_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 9. 课程附件表
CREATE TABLE `course_attachment` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `course_id` int(11) NOT NULL DEFAULT 0 COMMENT '课程ID',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '升序',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '附件名',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '附件类型',
  `rid` int(11) NOT NULL DEFAULT 0 COMMENT '资源ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `course_id` (`course_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程附件表';

-- 10. 课程章节表
CREATE TABLE `course_chapters` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `course_id` int(11) NOT NULL DEFAULT 0 COMMENT '课程ID',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '章节名',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '升序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程章节表';

-- 11. 课程表
CREATE TABLE `courses` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '课程标题',
  `thumb` int(11) NOT NULL DEFAULT 0 COMMENT '课程封面',
  `charge` int(11) NOT NULL DEFAULT 0 COMMENT '课程价格(分)',
  `short_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '简短介绍',
  `original_desc` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '原始介绍',
  `render_desc` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '渲染介绍',
  `seo_keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'SEO关键字',
  `seo_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'SEO描述',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '上架时间',
  `is_required` tinyint(4) NOT NULL DEFAULT 0 COMMENT '必修课程[1:是,0:否]',
  `class_hour` int(11) NOT NULL DEFAULT 0 COMMENT '课时数',
  `is_show` tinyint(4) NOT NULL DEFAULT 0 COMMENT '显示[1:是,0:否]',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `sort_at` timestamp NULL DEFAULT NULL COMMENT '排序时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程表';

-- 12. 课程小时表
CREATE TABLE `course_hours` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `course_id` int(11) NOT NULL DEFAULT 0 COMMENT '课程ID',
  `chapter_id` int(11) NOT NULL DEFAULT 0 COMMENT '章节ID',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '升序',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '课时标题',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '课时类型',
  `rid` int(11) NOT NULL DEFAULT 0 COMMENT '资源id',
  `duration` int(11) NOT NULL DEFAULT 0 COMMENT '时长[s]',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '上架时间',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `course_id` (`course_id`),
  KEY `chapter_id` (`chapter_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程小时表';

-- 13. 部门表
CREATE TABLE `departments` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '部门名',
  `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '父级部门id',
  `parent_chain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '父级链',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '升序',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- 14. 资源分类表
CREATE TABLE `resource_categories` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '父级分类id',
  `parent_chain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '父级链',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分类名',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '升序',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源分类表';

-- 15. 资源表
CREATE TABLE `resource` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '资源类型',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '资源名',
  `extension` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '文件后缀',
  `size` bigint(20) NOT NULL DEFAULT 0 COMMENT '文件大小[字节]',
  `disk` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '存储磁盘',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '相对地址',
  `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属素材',
  `is_hidden` tinyint(4) NOT NULL DEFAULT 0 COMMENT '隐藏[0:否,1:是]',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源表';

-- 16. 用户表
CREATE TABLE `users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `email` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '邮箱',
  `name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '真实姓名',
  `avatar` int(11) NOT NULL DEFAULT 0 COMMENT '头像',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密码',
  `salt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'salt',
  `id_card` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '身份证号',
  `credit1` int(11) NOT NULL DEFAULT 0 COMMENT '学分',
  `create_ip` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '注册IP',
  `create_city` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '注册城市',
  `verify_at` timestamp NULL DEFAULT NULL COMMENT '邮箱验证时间',
  `login_at` timestamp NULL DEFAULT NULL COMMENT '登录时间',
  `is_lock` tinyint(4) NOT NULL DEFAULT 0 COMMENT '锁定[1:是,0:否]',
  `is_active` tinyint(4) NOT NULL DEFAULT 0 COMMENT '激活[1:是,0:否]',
  `is_verify` tinyint(4) NOT NULL DEFAULT 0 COMMENT '邮箱验证[1:是,0:否]',
  `is_set_password` tinyint(4) NOT NULL DEFAULT 0 COMMENT '设置密码[1:是,0:否]',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 17. 用户课程记录表
CREATE TABLE `user_course_records` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `course_id` int(11) NOT NULL DEFAULT 0 COMMENT '课程ID',
  `hour_count` int(11) NOT NULL DEFAULT 0 COMMENT '课时数量',
  `finished_count` int(11) NOT NULL DEFAULT 0 COMMENT '已完成课时数量',
  `progress` int(11) NOT NULL DEFAULT 0 COMMENT '进度[0-100]',
  `watched_seconds` int(11) NOT NULL DEFAULT 0 COMMENT '已观看秒数',
  `is_finished` tinyint(4) NOT NULL DEFAULT 0 COMMENT '看完[1:是,0:否]',
  `finished_at` timestamp NULL DEFAULT NULL COMMENT '看完时间',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_course_records_user_id_course_id_unique` (`user_id`,`course_id`),
  KEY `user_id` (`user_id`),
  KEY `course_id` (`course_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户课程记录表';

-- 18. 用户课程小时记录表
CREATE TABLE `user_course_hour_records` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `course_id` int(11) NOT NULL DEFAULT 0 COMMENT '课程ID',
  `hour_id` int(11) NOT NULL DEFAULT 0 COMMENT '课时ID',
  `total_seconds` int(11) NOT NULL DEFAULT 0 COMMENT '总时长',
  `finished_seconds` int(11) NOT NULL DEFAULT 0 COMMENT '已完成时长',
  `real_watched_seconds` int(11) NOT NULL DEFAULT 0 COMMENT '实际观看时长',
  `is_finished` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否看完[1:是,0:否]',
  `finished_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_course_hour_records_user_id_course_id_hour_id_unique` (`user_id`,`course_id`,`hour_id`),
  KEY `user_id` (`user_id`),
  KEY `course_id` (`course_id`),
  KEY `hour_id` (`hour_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户课程小时记录表';

-- 19. 用户部门关联表
CREATE TABLE `user_department` (
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `dep_id` int(11) NOT NULL DEFAULT 0 COMMENT '部门ID',
  PRIMARY KEY (`user_id`,`dep_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户部门关联表';

-- 20. 课程部门用户关联表
CREATE TABLE `course_department_user` (
  `course_id` int(11) NOT NULL DEFAULT 0 COMMENT '课程ID',
  `range_id` int(11) NOT NULL DEFAULT 0 COMMENT '指派范围ID',
  `type` int(11) NOT NULL DEFAULT 0 COMMENT '指派范围类型[0:部门,1:学员]',
  PRIMARY KEY (`course_id`,`range_id`,`type`),
  KEY `course_id` (`course_id`),
  KEY `range_id` (`range_id`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程部门用户关联表';

-- 21. 用户学习时长记录表
CREATE TABLE `user_learn_duration_records` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `created_date` date NOT NULL COMMENT '创建日期',
  `duration` int(11) NOT NULL DEFAULT 0 COMMENT '已学习时长[秒]',
  `start_at` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_at` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `from_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '来源ID',
  `from_scene` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '来源场景',
  `course_id` int(11) NOT NULL DEFAULT 0 COMMENT '课程ID',
  `hour_id` int(11) NOT NULL DEFAULT 0 COMMENT '课时ID',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `created_date` (`created_date`),
  KEY `course_id` (`course_id`),
  KEY `hour_id` (`hour_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户学习时长记录表';

-- 22. 用户学习时长统计表
CREATE TABLE `user_learn_duration_stats` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `duration` bigint(20) NOT NULL DEFAULT 0 COMMENT '学习时长[秒]',
  `created_date` date NOT NULL COMMENT '创建日期',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_learn_duration_stats_user_id_created_date_unique` (`user_id`,`created_date`),
  KEY `user_id` (`user_id`),
  KEY `created_date` (`created_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户学习时长统计表';

-- 23. 资源分类关联表
CREATE TABLE `resource_category` (
  `cid` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `rid` int(11) NOT NULL DEFAULT 0 COMMENT '资源ID',
  PRIMARY KEY (`cid`,`rid`),
  KEY `cid` (`cid`),
  KEY `rid` (`rid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源分类关联表';

-- 24. 课程分类关联表
CREATE TABLE `resource_course_category` (
  `course_id` int(11) NOT NULL DEFAULT 0 COMMENT '课程ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  PRIMARY KEY (`course_id`,`category_id`),
  KEY `course_id` (`course_id`),
  KEY `category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程分类关联表';

-- 20. 用户学习时长记录表
CREATE TABLE `user_learn_duration_records` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `created_date` date NOT NULL COMMENT '创建日期',
  `duration` int(11) NOT NULL DEFAULT 0 COMMENT '已学习时长[秒]',
  `start_at` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_at` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `from_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '来源ID',
  `from_scene` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '来源场景',
  `course_id` int(11) NOT NULL DEFAULT 0 COMMENT '课程ID',
  `hour_id` int(11) NOT NULL DEFAULT 0 COMMENT '课时ID',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `created_date` (`created_date`),
  KEY `course_id` (`course_id`),
  KEY `hour_id` (`hour_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户学习时长记录表';

-- 21. 用户学习时长统计表
CREATE TABLE `user_learn_duration_stats` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `duration` bigint(20) NOT NULL DEFAULT 0 COMMENT '学习时长[秒]',
  `created_date` date NOT NULL COMMENT '创建日期',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_learn_duration_stats_user_id_created_date_unique` (`user_id`,`created_date`),
  KEY `user_id` (`user_id`),
  KEY `created_date` (`created_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户学习时长统计表';

-- 25. 资源详细信息表
CREATE TABLE `resource_extra` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `rid` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '资源ID',
  `poster` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '封面资源ID',
  `duration` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '视频、音频总时长,文档总页数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `rid` (`rid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源详细信息表';

-- 26. LDAP用户表
CREATE TABLE `ldap_user` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(64) NOT NULL DEFAULT '' COMMENT '唯一特征值',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `cn` varchar(120) NOT NULL DEFAULT '' COMMENT 'cn',
  `dn` varchar(120) NOT NULL DEFAULT '' COMMENT 'dn',
  `ou` varchar(255) NOT NULL DEFAULT '' COMMENT 'ou',
  `uid` varchar(120) NOT NULL DEFAULT '' COMMENT 'uid',
  `email` varchar(120) NOT NULL DEFAULT '' COMMENT '邮箱',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_uuid` (`uuid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LDAP用户表';

-- 27. LDAP用户同步详情表
CREATE TABLE `ldap_user_sync_detail` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(64) NOT NULL DEFAULT '' COMMENT '唯一特征值',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `cn` varchar(120) NOT NULL DEFAULT '' COMMENT 'cn',
  `dn` varchar(120) NOT NULL DEFAULT '' COMMENT 'dn',
  `ou` varchar(255) NOT NULL DEFAULT '' COMMENT 'ou',
  `uid` varchar(120) NOT NULL DEFAULT '' COMMENT 'uid',
  `email` varchar(120) NOT NULL DEFAULT '' COMMENT '邮箱',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LDAP用户同步详情表';

-- 28. SCORM包信息表
CREATE TABLE `scorm_packages` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resource_id` int(11) NOT NULL COMMENT '资源ID',
  `scorm_version` varchar(10) NOT NULL COMMENT 'SCORM版本：1.2, 2004',
  `identifier` varchar(255) DEFAULT NULL COMMENT '包标识符',
  `title` varchar(500) DEFAULT NULL COMMENT '包标题',
  `description` text COMMENT '包描述',
  `version` varchar(50) DEFAULT NULL COMMENT '包版本',
  `launch_file` varchar(500) DEFAULT NULL COMMENT '启动文件路径',
  `extract_path` varchar(500) DEFAULT NULL COMMENT '解压后的目录路径',
  `manifest_content` longtext COMMENT 'manifest.xml内容',
  `organization_json` longtext COMMENT '组织结构JSON',
  `resources_json` longtext COMMENT '资源清单JSON',
  `supports_navigation` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否支持导航：0-否，1-是',
  `time_limit` int(11) DEFAULT NULL COMMENT '最大时间限制（分钟）',
  `completion_threshold` decimal(5,4) DEFAULT NULL COMMENT '完成阈值（0-1）',
  `mastery_score` decimal(5,2) DEFAULT NULL COMMENT '掌握分数',
  `parse_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '解析状态：0-待解析，1-解析成功，2-解析失败',
  `parse_error` text COMMENT '解析错误信息',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_resource_id` (`resource_id`),
  KEY `idx_scorm_version` (`scorm_version`),
  KEY `idx_parse_status` (`parse_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SCORM包信息表';

-- 29. SCORM学习尝试表
CREATE TABLE `scorm_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `package_id` int(11) NOT NULL COMMENT 'SCORM包ID',
  `hour_id` int(11) DEFAULT NULL COMMENT '课程小时ID',
  `attempt_number` int(11) NOT NULL DEFAULT '1' COMMENT '尝试次数',
  `lesson_status` varchar(50) DEFAULT 'not attempted' COMMENT '课程状态',
  `completion_status` varchar(50) DEFAULT 'not attempted' COMMENT '完成状态',
  `success_status` varchar(50) DEFAULT 'unknown' COMMENT '成功状态',
  `score_raw` decimal(10,4) DEFAULT NULL COMMENT '原始分数',
  `score_max` decimal(10,4) DEFAULT NULL COMMENT '最高分数',
  `score_min` decimal(10,4) DEFAULT NULL COMMENT '最低分数',
  `score_scaled` decimal(5,4) DEFAULT NULL COMMENT '标准化分数',
  `progress_measure` decimal(5,4) DEFAULT NULL COMMENT '进度测量',
  `location` varchar(1000) DEFAULT NULL COMMENT '位置信息',
  `suspend_data` longtext COMMENT '暂停数据',
  `entry` varchar(50) DEFAULT 'ab-initio' COMMENT '进入方式',
  `exit` varchar(50) DEFAULT NULL COMMENT '退出方式',
  `mode` varchar(50) DEFAULT 'normal' COMMENT '模式',
  `credit` varchar(50) DEFAULT 'credit' COMMENT '学分',
  `total_time` varchar(50) DEFAULT NULL COMMENT '总时间',
  `session_time` varchar(50) DEFAULT NULL COMMENT '会话时间',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_package` (`user_id`, `package_id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_hour_id` (`hour_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SCORM学习尝试表';


-- 创建缺失的表
USE playedu;

-- 1. 资源详细信息表
CREATE TABLE `resource_extra` (
                                  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                  `rid` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '资源ID',
                                  `poster` int(11) unsigned NOT NULL DEFAULT 0 COMMENT '封面资源ID',
                                  `duration` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '视频、音频总时长,文档总页数',
                                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  PRIMARY KEY (`id`),
                                  UNIQUE KEY `rid` (`rid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源详细信息表';

-- 2. LDAP用户表
CREATE TABLE `ldap_user` (
                             `id` int unsigned NOT NULL AUTO_INCREMENT,
                             `uuid` varchar(64) NOT NULL DEFAULT '' COMMENT '唯一特征值',
                             `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
                             `cn` varchar(120) NOT NULL DEFAULT '' COMMENT 'cn',
                             `dn` varchar(120) NOT NULL DEFAULT '' COMMENT 'dn',
                             `ou` varchar(255) NOT NULL DEFAULT '' COMMENT 'ou',
                             `uid` varchar(120) NOT NULL DEFAULT '' COMMENT 'uid',
                             `email` varchar(120) NOT NULL DEFAULT '' COMMENT '邮箱',
                             `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                             `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                             PRIMARY KEY (`id`),
                             UNIQUE KEY `unique_uuid` (`uuid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LDAP用户表';

-- 3. LDAP用户同步详情表
CREATE TABLE `ldap_user_sync_detail` (
                                         `id` int unsigned NOT NULL AUTO_INCREMENT,
                                         `uuid` varchar(64) NOT NULL DEFAULT '' COMMENT '唯一特征值',
                                         `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
                                         `cn` varchar(120) NOT NULL DEFAULT '' COMMENT 'cn',
                                         `dn` varchar(120) NOT NULL DEFAULT '' COMMENT 'dn',
                                         `ou` varchar(255) NOT NULL DEFAULT '' COMMENT 'ou',
                                         `uid` varchar(120) NOT NULL DEFAULT '' COMMENT 'uid',
                                         `email` varchar(120) NOT NULL DEFAULT '' COMMENT '邮箱',
                                         `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                         `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LDAP用户同步详情表';

-- 4. SCORM包信息表
CREATE TABLE `scorm_packages` (
                                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                  `resource_id` int(11) NOT NULL COMMENT '资源ID',
                                  `scorm_version` varchar(10) NOT NULL COMMENT 'SCORM版本：1.2, 2004',
                                  `identifier` varchar(255) DEFAULT NULL COMMENT '包标识符',
                                  `title` varchar(500) DEFAULT NULL COMMENT '包标题',
                                  `description` text COMMENT '包描述',
                                  `version` varchar(50) DEFAULT NULL COMMENT '包版本',
                                  `launch_file` varchar(500) DEFAULT NULL COMMENT '启动文件路径',
                                  `extract_path` varchar(500) DEFAULT NULL COMMENT '解压后的目录路径',
                                  `manifest_content` longtext COMMENT 'manifest.xml内容',
                                  `organization_json` longtext COMMENT '组织结构JSON',
                                  `resources_json` longtext COMMENT '资源清单JSON',
                                  `supports_navigation` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否支持导航：0-否，1-是',
                                  `time_limit` int(11) DEFAULT NULL COMMENT '最大时间限制（分钟）',
                                  `completion_threshold` decimal(5,4) DEFAULT NULL COMMENT '完成阈值（0-1）',
                                  `mastery_score` decimal(5,2) DEFAULT NULL COMMENT '掌握分数',
                                  `parse_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '解析状态：0-待解析，1-解析成功，2-解析失败',
                                  `parse_error` text COMMENT '解析错误信息',
                                  `created_at` datetime NOT NULL COMMENT '创建时间',
                                  `updated_at` datetime NOT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`),
                                  UNIQUE KEY `uk_resource_id` (`resource_id`),
                                  KEY `idx_scorm_version` (`scorm_version`),
                                  KEY `idx_parse_status` (`parse_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SCORM包信息表';

-- 5. SCORM学习尝试表
CREATE TABLE `scorm_attempts` (
                                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                  `user_id` int(11) NOT NULL COMMENT '用户ID',
                                  `package_id` int(11) NOT NULL COMMENT 'SCORM包ID',
                                  `hour_id` int(11) DEFAULT NULL COMMENT '课程小时ID',
                                  `attempt_number` int(11) NOT NULL DEFAULT '1' COMMENT '尝试次数',
                                  `lesson_status` varchar(50) DEFAULT 'not attempted' COMMENT '课程状态',
                                  `completion_status` varchar(50) DEFAULT 'not attempted' COMMENT '完成状态',
                                  `success_status` varchar(50) DEFAULT 'unknown' COMMENT '成功状态',
                                  `score_raw` decimal(10,4) DEFAULT NULL COMMENT '原始分数',
                                  `score_max` decimal(10,4) DEFAULT NULL COMMENT '最高分数',
                                  `score_min` decimal(10,4) DEFAULT NULL COMMENT '最低分数',
                                  `score_scaled` decimal(5,4) DEFAULT NULL COMMENT '标准化分数',
                                  `progress_measure` decimal(5,4) DEFAULT NULL COMMENT '进度测量',
                                  `location` varchar(1000) DEFAULT NULL COMMENT '位置信息',
                                  `suspend_data` longtext COMMENT '暂停数据',
                                  `entry` varchar(50) DEFAULT 'ab-initio' COMMENT '进入方式',
                                  `exit` varchar(50) DEFAULT NULL COMMENT '退出方式',
                                  `mode` varchar(50) DEFAULT 'normal' COMMENT '模式',
                                  `credit` varchar(50) DEFAULT 'credit' COMMENT '学分',
                                  `total_time` varchar(50) DEFAULT NULL COMMENT '总时间',
                                  `session_time` varchar(50) DEFAULT NULL COMMENT '会话时间',
                                  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
                                  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
                                  `created_at` datetime NOT NULL COMMENT '创建时间',
                                  `updated_at` datetime NOT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_user_package` (`user_id`, `package_id`),
                                  KEY `idx_package_id` (`package_id`),
                                  KEY `idx_hour_id` (`hour_id`),
                                  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SCORM学习尝试表';

