/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.course.service;

import xyz.playedu.course.domain.ScormAttempt;
import xyz.playedu.course.domain.ScormPackage;

import java.util.Map;

/**
 * SCORM服务接口
 * 
 * <AUTHOR> Team
 */
public interface ScormService {

    /**
     * 解析SCORM包
     * 
     * @param resourceId 资源ID
     * @param zipFilePath ZIP文件路径
     * @return SCORM包信息
     */
    ScormPackage parseScormPackage(Integer resourceId, String zipFilePath);

    /**
     * 获取SCORM包信息
     * 
     * @param resourceId 资源ID
     * @return SCORM包信息
     */
    ScormPackage getScormPackage(Integer resourceId);

    /**
     * 创建学习尝试
     * 
     * @param userId 用户ID
     * @param packageId SCORM包ID
     * @param hourId 课程小时ID
     * @return 学习尝试
     */
    ScormAttempt createAttempt(Integer userId, Integer packageId, Integer hourId);

    /**
     * 获取当前学习尝试
     * 
     * @param userId 用户ID
     * @param packageId SCORM包ID
     * @return 学习尝试
     */
    ScormAttempt getCurrentAttempt(Integer userId, Integer packageId);

    /**
     * 初始化SCORM运行时环境
     * 
     * @param attemptId 尝试ID
     * @return 初始化数据
     */
    Map<String, Object> initializeRuntime(Integer attemptId);

    /**
     * 获取SCORM数据模型值
     * 
     * @param attemptId 尝试ID
     * @param element 数据模型元素
     * @return 值
     */
    String getValue(Integer attemptId, String element);

    /**
     * 设置SCORM数据模型值
     * 
     * @param attemptId 尝试ID
     * @param element 数据模型元素
     * @param value 值
     * @return 是否成功
     */
    boolean setValue(Integer attemptId, String element, String value);

    /**
     * 提交学习数据
     * 
     * @param attemptId 尝试ID
     * @return 是否成功
     */
    boolean commit(Integer attemptId);

    /**
     * 终止学习会话
     * 
     * @param attemptId 尝试ID
     * @return 是否成功
     */
    boolean terminate(Integer attemptId);

    /**
     * 获取最后错误信息
     * 
     * @param attemptId 尝试ID
     * @return 错误代码
     */
    String getLastError(Integer attemptId);

    /**
     * 获取错误描述
     * 
     * @param attemptId 尝试ID
     * @param errorCode 错误代码
     * @return 错误描述
     */
    String getErrorString(Integer attemptId, String errorCode);

    /**
     * 获取诊断信息
     * 
     * @param attemptId 尝试ID
     * @param errorCode 错误代码
     * @return 诊断信息
     */
    String getDiagnostic(Integer attemptId, String errorCode);

    /**
     * 验证SCORM包格式
     * 
     * @param zipFilePath ZIP文件路径
     * @return 是否有效
     */
    boolean validateScormPackage(String zipFilePath);

    /**
     * 获取SCORM版本
     * 
     * @param manifestContent manifest.xml内容
     * @return SCORM版本
     */
    String getScormVersion(String manifestContent);

    /**
     * 解析组织结构
     * 
     * @param manifestContent manifest.xml内容
     * @return 组织结构JSON
     */
    String parseOrganization(String manifestContent);

    /**
     * 解析资源清单
     * 
     * @param manifestContent manifest.xml内容
     * @return 资源清单JSON
     */
    String parseResources(String manifestContent);

    /**
     * 获取启动文件
     * 
     * @param manifestContent manifest.xml内容
     * @return 启动文件路径
     */
    String getLaunchFile(String manifestContent);
}
