<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.course.mapper.CourseAttachmentMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.course.domain.CourseAttachment">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="courseId" column="course_id" jdbcType="INTEGER"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="rid" column="rid" jdbcType="INTEGER"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,course_id,
        sort,title,type,
        rid,created_at
    </sql>
</mapper>
