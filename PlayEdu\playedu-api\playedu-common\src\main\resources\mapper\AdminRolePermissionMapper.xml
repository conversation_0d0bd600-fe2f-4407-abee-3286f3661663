<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.common.mapper.AdminRolePermissionMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.common.domain.AdminRolePermission">
            <result property="roleId" column="role_id" jdbcType="INTEGER"/>
            <result property="permId" column="perm_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        role_id,perm_id
    </sql>
</mapper>
