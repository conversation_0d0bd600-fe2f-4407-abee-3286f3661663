import React, { useEffect, useRef, useState } from "react";
import { Mo<PERSON>, Spin, message, Button, Progress } from "antd";
import { FullscreenOutlined, FullscreenExitOutlined } from "@ant-design/icons";
import { scorm } from "../../api/index";
import styles from "./index.module.less";

interface Props {
  packageId: number;
  hourId?: number;
  visible: boolean;
  onClose: () => void;
  onComplete?: (data: any) => void;
}

export const ScormPlayer: React.FC<Props> = ({
  packageId,
  hourId,
  visible,
  onClose,
  onComplete,
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [loading, setLoading] = useState(true);
  const [attemptId, setAttemptId] = useState<number | null>(null);
  const [launchUrl, setLaunchUrl] = useState<string>("");
  const [apiUrl, setApiUrl] = useState<string>("");
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [progress, setProgress] = useState(0);
  const [lessonStatus, setLessonStatus] = useState("not attempted");

  // SCORM API 对象
  const scormAPI = useRef<any>(null);

  useEffect(() => {
    if (visible && packageId) {
      initializeScorm();
    }
  }, [visible, packageId]);

  const initializeScorm = async () => {
    try {
      setLoading(true);
      const response = await scorm.initialize(packageId, hourId);
      
      setAttemptId(response.data.attempt_id);
      setLaunchUrl(response.data.launch_url);
      setApiUrl(response.data.api_url);
      
      // 创建SCORM API对象
      createScormAPI(response.data.attempt_id);
      
      setLoading(false);
    } catch (error: any) {
      message.error(error.message || "初始化SCORM失败");
      setLoading(false);
      onClose();
    }
  };

  const createScormAPI = (attemptId: number) => {
    // SCORM 1.2 API
    const API = {
      LMSInitialize: (param: string) => {
        console.log("LMSInitialize called with:", param);
        return "true";
      },

      LMSFinish: (param: string) => {
        console.log("LMSFinish called with:", param);
        terminateScorm();
        return "true";
      },

      LMSGetValue: (element: string) => {
        console.log("LMSGetValue called with:", element);
        return getScormValue(element);
      },

      LMSSetValue: (element: string, value: string) => {
        console.log("LMSSetValue called with:", element, value);
        return setScormValue(element, value);
      },

      LMSCommit: (param: string) => {
        console.log("LMSCommit called with:", param);
        return commitScormData();
      },

      LMSGetLastError: () => {
        return "0";
      },

      LMSGetErrorString: (errorCode: string) => {
        return "";
      },

      LMSGetDiagnostic: (errorCode: string) => {
        return "";
      }
    };

    // SCORM 2004 API
    const API_1484_11 = {
      Initialize: (param: string) => {
        console.log("Initialize called with:", param);
        return "true";
      },

      Terminate: (param: string) => {
        console.log("Terminate called with:", param);
        terminateScorm();
        return "true";
      },

      GetValue: (element: string) => {
        console.log("GetValue called with:", element);
        return getScormValue(element);
      },

      SetValue: (element: string, value: string) => {
        console.log("SetValue called with:", element, value);
        return setScormValue(element, value);
      },

      Commit: (param: string) => {
        console.log("Commit called with:", param);
        return commitScormData();
      },

      GetLastError: () => {
        return "0";
      },

      GetErrorString: (errorCode: string) => {
        return "";
      },

      GetDiagnostic: (errorCode: string) => {
        return "";
      }
    };

    scormAPI.current = { API, API_1484_11 };

    // 将API对象暴露给iframe
    if (window) {
      (window as any).API = API;
      (window as any).API_1484_11 = API_1484_11;
    }
  };

  const getScormValue = async (element: string) => {
    try {
      const response = await scorm.getData(attemptId!, element);
      return response.data.value || "";
    } catch (error) {
      console.error("获取SCORM数据失败:", error);
      return "";
    }
  };

  const setScormValue = async (element: string, value: string) => {
    try {
      const response = await scorm.setData(attemptId!, element, value);

      // 更新本地状态
      if (element.includes("lesson_status") || element.includes("completion_status")) {
        setLessonStatus(value);
        updateProgress(value);
      }

      return response.data.success ? "true" : "false";
    } catch (error) {
      console.error("设置SCORM数据失败:", error);
      return "false";
    }
  };

  const commitScormData = async () => {
    try {
      const response = await scorm.commit(attemptId!);
      return response.data.success ? "true" : "false";
    } catch (error) {
      console.error("提交SCORM数据失败:", error);
      return "false";
    }
  };

  const terminateScorm = async () => {
    try {
      if (attemptId) {
        const response = await scorm.terminate(attemptId);
        if (response.data.success && onComplete) {
          onComplete(response.data);
        }
      }
    } catch (error) {
      console.error("终止SCORM会话失败:", error);
    }
  };

  const updateProgress = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
      case "passed":
        setProgress(100);
        break;
      case "incomplete":
      case "failed":
        setProgress(50);
        break;
      case "browsed":
        setProgress(25);
        break;
      default:
        setProgress(0);
    }
  };

  const handleIframeLoad = () => {
    // 确保API对象在iframe加载后可用
    if (iframeRef.current && iframeRef.current.contentWindow) {
      const iframeWindow = iframeRef.current.contentWindow as any;
      iframeWindow.API = scormAPI.current?.API;
      iframeWindow.API_1484_11 = scormAPI.current?.API_1484_11;
    }
  };

  const handleClose = () => {
    if (attemptId) {
      terminateScorm();
    }
    onClose();
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <Modal
      title="SCORM课件播放器"
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={isFullscreen ? "100%" : "90%"}
      style={{ top: isFullscreen ? 0 : 20 }}
      bodyStyle={{
        height: isFullscreen ? "100vh" : "80vh",
        padding: 0,
        position: "relative"
      }}
      destroyOnClose
      className={isFullscreen ? styles["fullscreen-modal"] : ""}
    >
      <div className={styles["scorm-player"]}>
        {loading ? (
          <div className={styles["loading-container"]}>
            <Spin size="large" />
            <div>正在加载SCORM课件...</div>
          </div>
        ) : (
          <>
            <div className={styles["scorm-controls"]}>
              <Button
                type="text"
                icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                onClick={toggleFullscreen}
                className={styles["control-button"]}
                title={isFullscreen ? "退出全屏" : "全屏"}
              />
            </div>

            <iframe
              ref={iframeRef}
              src={launchUrl}
              className={styles["scorm-iframe"]}
              onLoad={handleIframeLoad}
              title="SCORM Content"
              sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"
            />

            <div className={styles["scorm-progress"]}>
              <div className={styles["progress-info"]}>
                <div className={styles["progress-text"]}>
                  <span>状态: {lessonStatus}</span>
                  <span>进度: {progress}%</span>
                </div>
                <div className={styles["progress-bar"]}>
                  <Progress
                    percent={progress}
                    size="small"
                    status={lessonStatus === "completed" || lessonStatus === "passed" ? "success" : "active"}
                  />
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default ScormPlayer;
