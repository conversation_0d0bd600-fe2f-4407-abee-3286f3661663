/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.course.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import xyz.playedu.course.domain.ScormPackage;
import xyz.playedu.course.mapper.ScormPackageMapper;
import xyz.playedu.course.service.ScormPackageService;

import java.util.Date;

/**
 * SCORM包服务实现类
 *
 * <AUTHOR> Team
 */
@Service
public class ScormPackageServiceImpl extends ServiceImpl<ScormPackageMapper, ScormPackage> implements ScormPackageService {

    @Override
    public ScormPackage getByResourceId(Integer resourceId) {
        QueryWrapper<ScormPackage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("resource_id", resourceId);
        return getOne(queryWrapper);
    }

    @Override
    public ScormPackage createPackage(ScormPackage scormPackage) {
        scormPackage.setCreatedAt(new Date());
        scormPackage.setUpdatedAt(new Date());
        save(scormPackage);
        return scormPackage;
    }

    @Override
    public ScormPackage updatePackage(Integer id, ScormPackage scormPackage) {
        scormPackage.setId(id);
        scormPackage.setUpdatedAt(new Date());
        updateById(scormPackage);
        return getById(id);
    }

    @Override
    public void deletePackage(Integer id) {
        removeById(id);
    }

    @Override
    public boolean reparsePackage(Integer id) {
        ScormPackage scormPackage = getById(id);
        if (scormPackage == null) {
            return false;
        }

        // 重置解析状态为待解析
        scormPackage.setParseStatus(0);
        scormPackage.setParseError(null);
        scormPackage.setUpdatedAt(new Date());
        updateById(scormPackage);

        // TODO: 触发重新解析任务
        return true;
    }

    @Override
    public void updateParseStatus(Integer id, Integer status, String error) {
        ScormPackage scormPackage = getById(id);
        if (scormPackage != null) {
            scormPackage.setParseStatus(status);
            scormPackage.setParseError(error);
            scormPackage.setUpdatedAt(new Date());
            updateById(scormPackage);
        }
    }
}
