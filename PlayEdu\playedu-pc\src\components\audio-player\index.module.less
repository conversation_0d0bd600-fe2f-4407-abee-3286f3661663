.audio-player {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;

  .audio-title {
    margin-bottom: 16px;
    text-align: center;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
  }

  .audio-controls {
    display: flex;
    align-items: center;
    gap: 16px;

    .play-controls {
      flex-shrink: 0;

      .play-button {
        border: none;
        box-shadow: none;
        color: #1890ff;
        
        &:hover {
          color: #40a9ff;
        }

        &:disabled {
          color: #d9d9d9;
        }
      }
    }

    .progress-controls {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 12px;

      .time-text {
        font-size: 12px;
        color: #666;
        min-width: 40px;
        text-align: center;
      }

      .progress-slider {
        flex: 1;
        margin: 0;

        :global(.ant-slider-rail) {
          background-color: #f5f5f5;
        }

        :global(.ant-slider-track) {
          background-color: #1890ff;
        }

        :global(.ant-slider-handle) {
          border-color: #1890ff;
          
          &:hover {
            border-color: #40a9ff;
          }

          &:focus {
            border-color: #40a9ff;
            box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.12);
          }
        }
      }
    }

    .volume-controls {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 120px;

      .volume-button {
        border: none;
        box-shadow: none;
        color: #666;
        
        &:hover {
          color: #1890ff;
        }
      }

      .volume-slider {
        width: 80px;
        margin: 0;

        :global(.ant-slider-rail) {
          background-color: #f5f5f5;
        }

        :global(.ant-slider-track) {
          background-color: #1890ff;
        }

        :global(.ant-slider-handle) {
          border-color: #1890ff;
          
          &:hover {
            border-color: #40a9ff;
          }

          &:focus {
            border-color: #40a9ff;
            box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.12);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .audio-player {
    padding: 16px;

    .audio-controls {
      flex-direction: column;
      gap: 12px;

      .progress-controls {
        width: 100%;
        order: 1;
      }

      .play-controls {
        order: 2;
      }

      .volume-controls {
        order: 3;
        justify-content: center;
      }
    }
  }
}

@media (max-width: 480px) {
  .audio-player {
    padding: 12px;

    .audio-controls {
      .volume-controls {
        .volume-slider {
          width: 60px;
        }
      }
    }
  }
}
