<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.resource.mapper.ResourceExtraMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.resource.domain.ResourceExtra">
            <result property="rid" column="rid" jdbcType="INTEGER"/>
            <result property="poster" column="poster" jdbcType="VARCHAR"/>
            <result property="duration" column="duration" jdbcType="INTEGER"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        rid,poster,duration,
        created_at
    </sql>
</mapper>
