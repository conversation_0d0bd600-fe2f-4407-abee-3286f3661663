import { useEffect, useState } from "react";
import {
  Table,
  Button,
  Space,
  Modal,
  message,
  Input,
  Tag,
  Tooltip,
  Progress,
  Card,
  Row,
  Col,
  Statistic,
} from "antd";
import {
  PlusOutlined,
  EyeOutlined,
  DeleteOutlined,
  ExclamationCircleFilled,
  FileZipOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  PlayCircleOutlined,
} from "@ant-design/icons";
import { scorm } from "../../../api/index";
import { UploadScormButton } from "../../../compenents/upload-scorm-button";
import { ScormPlayer } from "../../../compenents/scorm-player";
import styles from "./index.module.less";

const { Search } = Input;
const { confirm } = Modal;

interface ScormPackageItem {
  id: number;
  resource_id: number;
  scorm_version: string;
  title: string;
  description: string;
  launch_file: string;
  parse_status: number; // 0-待解析，1-解析成功，2-解析失败
  parse_error?: string;
  supports_navigation: number;
  completion_threshold?: number;
  mastery_score?: number;
  created_at: string;
  updated_at: string;
}

const ScormPackagesPage = () => {
  const [list, setList] = useState<ScormPackageItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [refresh, setRefresh] = useState(false);
  const [keywords, setKeywords] = useState("");
  const [statistics, setStatistics] = useState({
    total: 0,
    parsed: 0,
    parsing: 0,
    failed: 0,
  });
  const [playerVisible, setPlayerVisible] = useState(false);
  const [currentPackageId, setCurrentPackageId] = useState<number | null>(null);

  useEffect(() => {
    getData();
    getStatistics();
  }, [page, size, refresh, keywords]);

  const getData = () => {
    setLoading(true);
    scorm
      .packageList(page, size, keywords)
      .then((res: any) => {
        setList(res.data.data);
        setTotal(res.data.total);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const getStatistics = () => {
    scorm.packageStatistics().then((res: any) => {
      setStatistics(res.data);
    });
  };

  const resetData = () => {
    setPage(1);
    setList([]);
    setRefresh(!refresh);
  };

  const handlePlay = (packageId: number) => {
    setCurrentPackageId(packageId);
    setPlayerVisible(true);
  };

  const handlePlayerClose = () => {
    setPlayerVisible(false);
    setCurrentPackageId(null);
  };

  const handlePlayerComplete = (data: any) => {
    message.success("学习完成");
    // 可以在这里处理学习完成后的逻辑
  };

  const deletePackage = (id: number) => {
    confirm({
      title: "删除SCORM包",
      icon: <ExclamationCircleFilled />,
      content: "确认删除此SCORM包吗？删除后无法恢复。",
      onOk() {
        scorm.destroyPackage(id).then(() => {
          message.success("删除成功");
          resetData();
        });
      },
    });
  };

  const reparse = (id: number) => {
    confirm({
      title: "重新解析",
      content: "确认重新解析此SCORM包吗？",
      onOk() {
        scorm.reparsePackage(id).then(() => {
          message.success("重新解析已开始");
          resetData();
        });
      },
    });
  };

  const getStatusTag = (status: number, error?: string) => {
    switch (status) {
      case 0:
        return <Tag color="processing" icon={<ClockCircleOutlined />}>待解析</Tag>;
      case 1:
        return <Tag color="success" icon={<CheckCircleOutlined />}>解析成功</Tag>;
      case 2:
        return (
          <Tooltip title={error}>
            <Tag color="error" icon={<CloseCircleOutlined />}>解析失败</Tag>
          </Tooltip>
        );
      default:
        return <Tag>未知状态</Tag>;
    }
  };

  const getVersionTag = (version: string) => {
    const color = version === "1.2" ? "blue" : "green";
    return <Tag color={color}>SCORM {version}</Tag>;
  };

  const columns = [
    {
      title: "包标题",
      dataIndex: "title",
      key: "title",
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (text: string, record: ScormPackageItem) => (
        <div>
          <Tooltip placement="topLeft" title={text}>
            <div className={styles["package-title"]}>{text || "未命名包"}</div>
          </Tooltip>
          {record.description && (
            <div className={styles["package-desc"]}>{record.description}</div>
          )}
        </div>
      ),
    },
    {
      title: "SCORM版本",
      dataIndex: "scorm_version",
      key: "scorm_version",
      width: 120,
      render: (version: string) => getVersionTag(version),
    },
    {
      title: "解析状态",
      key: "parse_status",
      width: 120,
      render: (record: ScormPackageItem) => 
        getStatusTag(record.parse_status, record.parse_error),
    },
    {
      title: "启动文件",
      dataIndex: "launch_file",
      key: "launch_file",
      width: 150,
      ellipsis: true,
    },
    {
      title: "功能特性",
      key: "features",
      width: 150,
      render: (record: ScormPackageItem) => (
        <Space direction="vertical" size="small">
          {record.supports_navigation ? (
            <Tag size="small" color="blue">支持导航</Tag>
          ) : (
            <Tag size="small">无导航</Tag>
          )}
          {record.completion_threshold && (
            <Tag size="small" color="green">
              完成阈值: {(record.completion_threshold * 100).toFixed(0)}%
            </Tag>
          )}
          {record.mastery_score && (
            <Tag size="small" color="orange">
              掌握分数: {record.mastery_score}
            </Tag>
          )}
        </Space>
      ),
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      key: "created_at",
      width: 150,
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right" as const,
      render: (record: ScormPackageItem) => (
        <Space size="small">
          <Button
            size="small"
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={() => handlePlay(record.id)}
            disabled={record.parse_status !== 1}
          >
            播放
          </Button>
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              // 预览SCORM包
              window.open(`/scorm-preview/${record.id}`, "_blank");
            }}
            disabled={record.parse_status !== 1}
          >
            预览
          </Button>
          {record.parse_status === 2 && (
            <Button
              size="small"
              type="primary"
              onClick={() => reparse(record.id)}
            >
              重新解析
            </Button>
          )}
          <Button
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => deletePackage(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="playedu-main-body">
      <div className="playedu-main-title">SCORM包管理</div>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-24">
        <Col span={6}>
          <Card>
            <Statistic
              title="总包数"
              value={statistics.total}
              prefix={<FileZipOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="解析成功"
              value={statistics.parsed}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: "#52c41a" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="解析中"
              value={statistics.parsing}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: "#faad14" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="解析失败"
              value={statistics.failed}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: "#ff4d4f" }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作栏 */}
      <div className="playedu-main-top mb-30">
        <div className="playedu-main-top-left">
          <UploadScormButton
            onUpdate={() => {
              resetData();
            }}
          />
        </div>
        <div className="playedu-main-top-right">
          <Search
            placeholder="搜索SCORM包标题"
            style={{ width: 200 }}
            onSearch={(value) => {
              setKeywords(value);
              setPage(1);
            }}
            allowClear
          />
        </div>
      </div>

      {/* 表格 */}
      <div className="playedu-main-table">
        <Table
          columns={columns}
          dataSource={list}
          loading={loading}
          pagination={{
            current: page,
            pageSize: size,
            total: total,
            onChange: (page, pageSize) => {
              setPage(page);
              setSize(pageSize || 10);
            },
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 个SCORM包`,
          }}
          rowKey="id"
          scroll={{ x: 1000 }}
        />
      </div>

      {/* SCORM播放器 */}
      {currentPackageId && (
        <ScormPlayer
          packageId={currentPackageId}
          visible={playerVisible}
          onClose={handlePlayerClose}
          onComplete={handlePlayerComplete}
        />
      )}
    </div>
  );
};

export default ScormPackagesPage;
