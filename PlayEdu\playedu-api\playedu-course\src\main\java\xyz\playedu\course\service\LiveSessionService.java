/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import xyz.playedu.common.types.paginate.PaginationResult;
import xyz.playedu.course.domain.LiveSession;

import java.util.Date;
import java.util.List;

/**
 * 直播场次服务接口
 * 
 * <AUTHOR> Team
 */
public interface LiveSessionService extends IService<LiveSession> {

    /**
     * 分页查询直播场次
     * 
     * @param page 页码
     * @param size 每页大小
     * @param title 标题关键词
     * @param status 状态
     * @return 分页结果
     */
    PaginationResult<LiveSession> paginate(int page, int size, String title, Integer status);

    /**
     * 创建直播场次
     * 
     * @param title 标题
     * @param description 描述
     * @param thumb 封面
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param maxViewers 最大观看人数
     * @param isRecord 是否录制
     * @param adminId 创建人ID
     * @return 直播场次
     */
    LiveSession create(String title, String description, Integer thumb, Date startTime, 
                      Date endTime, Integer maxViewers, Integer isRecord, Integer adminId);

    /**
     * 更新直播场次
     * 
     * @param liveSession 直播场次
     * @param title 标题
     * @param description 描述
     * @param thumb 封面
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param maxViewers 最大观看人数
     * @param isRecord 是否录制
     */
    void update(LiveSession liveSession, String title, String description, Integer thumb, 
               Date startTime, Date endTime, Integer maxViewers, Integer isRecord);

    /**
     * 开始直播
     * 
     * @param id 直播场次ID
     * @return 推流地址
     */
    String startLive(Integer id);

    /**
     * 结束直播
     * 
     * @param id 直播场次ID
     */
    void endLive(Integer id);

    /**
     * 获取直播播放地址
     * 
     * @param id 直播场次ID
     * @return 播放地址
     */
    String getPlayUrl(Integer id);

    /**
     * 更新观看人数
     * 
     * @param id 直播场次ID
     * @param viewerCount 观看人数
     */
    void updateViewerCount(Integer id, Integer viewerCount);

    /**
     * 获取正在直播的场次
     * 
     * @return 直播场次列表
     */
    List<LiveSession> getLivingSessions();

    /**
     * 获取即将开始的直播
     * 
     * @param minutes 提前分钟数
     * @return 直播场次列表
     */
    List<LiveSession> getUpcomingSessions(Integer minutes);

    /**
     * 生成推流地址
     * 
     * @param sessionId 直播场次ID
     * @return 推流地址
     */
    String generatePushUrl(Integer sessionId);

    /**
     * 生成拉流地址
     * 
     * @param sessionId 直播场次ID
     * @return 拉流地址
     */
    String generatePullUrl(Integer sessionId);

    /**
     * 生成HLS播放地址
     * 
     * @param sessionId 直播场次ID
     * @return HLS播放地址
     */
    String generateHlsUrl(Integer sessionId);
}
