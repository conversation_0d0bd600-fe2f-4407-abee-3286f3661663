import client from "./internal/httpClient";

// 获取直播场次列表
export function sessionList(
  page: number,
  size: number,
  title?: string,
  status?: number
) {
  return client.get("/backend/v1/live/sessions", {
    page,
    size,
    title,
    status,
  });
}

// 获取直播统计信息
export function sessionStatistics() {
  return client.get("/backend/v1/live/sessions/statistics");
}

// 创建直播场次
export function createSession(data: {
  title: string;
  description: string;
  thumb?: number;
  start_time: string;
  end_time: string;
  max_viewers: number;
  is_record: number;
}) {
  return client.post("/backend/v1/live/sessions", data);
}

// 获取直播场次详情
export function sessionDetail(id: number) {
  return client.get(`/backend/v1/live/sessions/${id}`);
}

// 更新直播场次
export function updateSession(id: number, data: {
  title: string;
  description: string;
  thumb?: number;
  start_time: string;
  end_time: string;
  max_viewers: number;
  is_record: number;
}) {
  return client.put(`/backend/v1/live/sessions/${id}`, data);
}

// 删除直播场次
export function destroySession(id: number) {
  return client.delete(`/backend/v1/live/sessions/${id}`);
}

// 开始直播
export function startSession(id: number) {
  return client.post(`/backend/v1/live/sessions/${id}/start`);
}

// 结束直播
export function endSession(id: number) {
  return client.post(`/backend/v1/live/sessions/${id}/end`);
}

// 获取推流地址
export function getPushUrl(id: number) {
  return client.get(`/backend/v1/live/sessions/${id}/push-url`);
}

// 获取播放地址
export function getPlayUrl(id: number) {
  return client.get(`/backend/v1/live/sessions/${id}/play-url`);
}

// 更新观看人数
export function updateViewerCount(id: number, count: number) {
  return client.post(`/backend/v1/live/sessions/${id}/viewer-count`, {
    count,
  });
}

// 获取直播回放列表
export function playbackList(sessionId: number) {
  return client.get(`/backend/v1/live/sessions/${sessionId}/playbacks`);
}

// 生成直播回放
export function generatePlayback(sessionId: number) {
  return client.post(`/backend/v1/live/sessions/${sessionId}/generate-playback`);
}
