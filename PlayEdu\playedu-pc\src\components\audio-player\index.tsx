import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>r, Button, Space, Typography } from "antd";
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  SoundOutlined,
  MutedOutlined,
} from "@ant-design/icons";
import styles from "./index.module.less";

const { Text } = Typography;

interface AudioPlayerProps {
  src: string;
  title?: string;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onEnded?: () => void;
  onPlay?: () => void;
  onPause?: () => void;
  autoPlay?: boolean;
  showTitle?: boolean;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({
  src,
  title,
  onTimeUpdate,
  onEnded,
  onPlay,
  onPause,
  autoPlay = false,
  showTitle = true,
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
      setIsLoading(false);
    };

    const handleTimeUpdate = () => {
      const current = audio.currentTime;
      setCurrentTime(current);
      onTimeUpdate?.(current, audio.duration);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      onEnded?.();
    };

    const handlePlay = () => {
      setIsPlaying(true);
      onPlay?.();
    };

    const handlePause = () => {
      setIsPlaying(false);
      onPause?.();
    };

    const handleLoadStart = () => {
      setIsLoading(true);
    };

    const handleCanPlay = () => {
      setIsLoading(false);
    };

    audio.addEventListener("loadedmetadata", handleLoadedMetadata);
    audio.addEventListener("timeupdate", handleTimeUpdate);
    audio.addEventListener("ended", handleEnded);
    audio.addEventListener("play", handlePlay);
    audio.addEventListener("pause", handlePause);
    audio.addEventListener("loadstart", handleLoadStart);
    audio.addEventListener("canplay", handleCanPlay);

    return () => {
      audio.removeEventListener("loadedmetadata", handleLoadedMetadata);
      audio.removeEventListener("timeupdate", handleTimeUpdate);
      audio.removeEventListener("ended", handleEnded);
      audio.removeEventListener("play", handlePlay);
      audio.removeEventListener("pause", handlePause);
      audio.removeEventListener("loadstart", handleLoadStart);
      audio.removeEventListener("canplay", handleCanPlay);
    };
  }, [onTimeUpdate, onEnded, onPlay, onPause]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
    }
  }, [volume]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.muted = isMuted;
    }
  }, [isMuted]);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
  };

  const handleProgressChange = (value: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.currentTime = value;
    setCurrentTime(value);
  };

  const handleVolumeChange = (value: number) => {
    setVolume(value);
    setIsMuted(value === 0);
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  };

  return (
    <div className={styles["audio-player"]}>
      <audio
        ref={audioRef}
        src={src}
        preload="metadata"
        autoPlay={autoPlay}
      />
      
      {showTitle && title && (
        <div className={styles["audio-title"]}>
          <Text strong>{title}</Text>
        </div>
      )}

      <div className={styles["audio-controls"]}>
        <div className={styles["play-controls"]}>
          <Button
            type="text"
            icon={
              isPlaying ? (
                <PauseCircleOutlined style={{ fontSize: "32px" }} />
              ) : (
                <PlayCircleOutlined style={{ fontSize: "32px" }} />
              )
            }
            onClick={togglePlay}
            disabled={isLoading}
            className={styles["play-button"]}
          />
        </div>

        <div className={styles["progress-controls"]}>
          <Text className={styles["time-text"]}>
            {formatTime(currentTime)}
          </Text>
          
          <Slider
            min={0}
            max={duration}
            value={currentTime}
            onChange={handleProgressChange}
            tooltip={{ formatter: (value) => formatTime(value || 0) }}
            className={styles["progress-slider"]}
            disabled={isLoading}
          />
          
          <Text className={styles["time-text"]}>
            {formatTime(duration)}
          </Text>
        </div>

        <div className={styles["volume-controls"]}>
          <Button
            type="text"
            icon={isMuted ? <MutedOutlined /> : <SoundOutlined />}
            onClick={toggleMute}
            className={styles["volume-button"]}
          />
          
          <Slider
            min={0}
            max={1}
            step={0.1}
            value={isMuted ? 0 : volume}
            onChange={handleVolumeChange}
            className={styles["volume-slider"]}
            tooltip={{ formatter: (value) => `${Math.round((value || 0) * 100)}%` }}
          />
        </div>
      </div>
    </div>
  );
};

export default AudioPlayer;
