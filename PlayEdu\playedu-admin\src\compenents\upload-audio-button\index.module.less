.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    border-color: #1890ff;
    background: #f0f8ff;
  }

  .upload-label {
    display: block;
    width: 100%;
    height: 120px;
    cursor: pointer;

    .upload-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 20px;

      .upload-text {
        margin-top: 12px;
        text-align: center;

        > div:first-child {
          font-size: 16px;
          color: #262626;
          margin-bottom: 4px;
        }

        .upload-hint {
          font-size: 12px;
          color: #8c8c8c;
          line-height: 1.4;
        }
      }
    }
  }
}

.file-info {
  margin-top: 12px;
  padding: 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;

  .file-details {
    display: flex;
    align-items: center;

    .file-name {
      font-weight: 500;
      color: #262626;
      margin-right: 8px;
    }

    .file-size {
      font-size: 12px;
      color: #8c8c8c;
    }
  }
}

.upload-progress {
  margin-top: 16px;

  .progress-text {
    text-align: center;
    margin-top: 8px;
    font-size: 14px;
    color: #1890ff;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .upload-area {
    .upload-label {
      height: 100px;

      .upload-content {
        padding: 16px;

        :global(.anticon) {
          font-size: 36px !important;
        }

        .upload-text {
          margin-top: 8px;

          > div:first-child {
            font-size: 14px;
          }

          .upload-hint {
            font-size: 11px;
          }
        }
      }
    }
  }

  .file-info {
    padding: 10px;

    .file-details {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;

      .file-name {
        margin-right: 0;
      }
    }
  }
}

@media (max-width: 480px) {
  .upload-area {
    .upload-label {
      height: 80px;

      .upload-content {
        padding: 12px;

        :global(.anticon) {
          font-size: 28px !important;
        }

        .upload-text {
          margin-top: 6px;

          > div:first-child {
            font-size: 13px;
          }

          .upload-hint {
            font-size: 10px;
          }
        }
      }
    }
  }
}
