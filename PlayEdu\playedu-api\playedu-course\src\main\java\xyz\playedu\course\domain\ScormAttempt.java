/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.course.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SCORM学习尝试记录表
 * @TableName scorm_attempts
 */
@TableName(value = "scorm_attempts")
@Data
public class ScormAttempt implements Serializable {
    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 用户ID */
    @JsonProperty("user_id")
    private Integer userId;

    /** SCORM包ID */
    @JsonProperty("package_id")
    private Integer packageId;

    /** 课程小时ID */
    @JsonProperty("hour_id")
    private Integer hourId;

    /** 尝试次数 */
    @JsonProperty("attempt_number")
    private Integer attemptNumber;

    /** 学习状态：not attempted, incomplete, completed, failed, passed, browsed */
    @JsonProperty("lesson_status")
    private String lessonStatus;

    /** 完成状态：completed, incomplete, not attempted, unknown */
    @JsonProperty("completion_status")
    private String completionStatus;

    /** 成功状态：passed, failed, unknown */
    @JsonProperty("success_status")
    private String successStatus;

    /** 分数（原始分数） */
    @JsonProperty("score_raw")
    private Double scoreRaw;

    /** 最大分数 */
    @JsonProperty("score_max")
    private Double scoreMax;

    /** 最小分数 */
    @JsonProperty("score_min")
    private Double scoreMin;

    /** 标准化分数（0-1） */
    @JsonProperty("score_scaled")
    @TableField(exist = false)
    private Double scoreScaled;

    /** 总学习时间（秒） */
    @JsonProperty("total_time")
    private Integer totalTime;

    /** 会话时间（秒） */
    @JsonProperty("session_time")
    @TableField(exist = false)
    private Integer sessionTime;

    /** 进度度量（0-1） */
    @JsonProperty("progress_measure")
    @TableField(exist = false)
    private Double progressMeasure;

    /** 学习者偏好（语言等） */
    @JsonProperty("learner_preference")
    @TableField(exist = false)
    private String learnerPreference;

    /** 位置信息（书签） */
    private String location;

    /** 暂停位置 */
    @JsonProperty("suspend_data")
    private String suspendData;

    /** 学习者评论 */
    @JsonProperty("learner_comments")
    @TableField(exist = false)
    private String learnerComments;

    /** 退出状态：time-out, suspend, logout, normal */
    @TableField("`exit`")
    private String exit;

    /** 入口状态：ab-initio, resume */
    @TableField("`entry`")
    private String entry;

    /** 模式：browse, normal, review */
    @TableField("`mode`")
    private String mode;

    /** 信用状态：credit, no-credit */
    private String credit;

    /** 开始时间 */
    @JsonProperty("start_time")
    private Date startTime;

    /** 结束时间 */
    @JsonProperty("end_time")
    private Date endTime;

    /** 创建时间 */
    @JsonProperty("created_at")
    private Date createdAt;

    /** 更新时间 */
    @JsonProperty("updated_at")
    private Date updatedAt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
