<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.resource.mapper.ResourceMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.resource.domain.Resource">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="adminId" column="admin_id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="extension" column="extension" jdbcType="VARCHAR"/>
        <result property="size" column="size" jdbcType="BIGINT"/>
        <result property="disk" column="disk" jdbcType="VARCHAR"/>
        <result property="path" column="path" jdbcType="VARCHAR"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
        <result property="isHidden" column="is_hidden" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,admin_id,type,
        name,extension,size,
        disk,path,created_at,parent_id,
        is_hidden
    </sql>

    <select id="paginate" resultType="xyz.playedu.resource.domain.Resource">
        SELECT `resource`.*
        FROM `resource`
        <choose>
            <when test="categoryIds != null and !categoryIds.isEmpty()">
                <choose>
                    <when test="categoryIds.get(0) == 0">
                        LEFT JOIN `resource_category` ON `resource_category`.`rid` = `resource`.`id`
                        WHERE `resource`.`is_hidden` = 0
                        AND `resource_category`.`cid` IS NULL
                    </when>
                    <otherwise>
                        INNER JOIN `resource_category` ON `resource_category`.`rid` = `resource`.`id`
                        WHERE `resource`.`is_hidden` = 0
                        AND `resource_category`.`cid` IN (<foreach collection="categoryIds" item="tmpId" separator=",">
                        #{tmpId}</foreach>)
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                WHERE `resource`.`is_hidden` = 0
            </otherwise>
        </choose>
        <if test="name != null and name != ''">
            AND `resource`.`name` LIKE concat('%',#{name},'%')
        </if>
        <if test="disk != null and disk != ''">
            AND `resource`.`disk` = #{disk}
        </if>
        <if test="extension != null and extension != ''">
            AND `resource`.`extension` = #{extension}
        </if>
        <if test="type != null and type != ''">
            AND `resource`.`type` IN
            <foreach item="item" collection="type.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="adminId != null and adminId != 0">
            AND `resource`.`admin_id` = #{adminId}
        </if>
        <if test="sortAlgo == 'asc'">
            <choose>
                <when test="sortField == 'size'">
                    ORDER BY `resource`.`size` ASC
                </when>
                <when test="sortField == 'created_at'">
                    ORDER BY `resource`.`created_at` ASC
                </when>
                <otherwise>
                    ORDER BY `resource`.`id` ASC
                </otherwise>
            </choose>
        </if>
        <if test="sortAlgo != 'asc'">
            <choose>
                <when test="sortField == 'size'">
                    ORDER BY `resource`.`size` DESC
                </when>
                <when test="sortField == 'created_at'">
                    ORDER BY `resource`.`created_at` DESC
                </when>
                <otherwise>
                    ORDER BY `resource`.`id` DESC
                </otherwise>
            </choose>
        </if>
        LIMIT #{pageStart}, #{pageSize};
    </select>

    <select id="paginateCount" resultType="java.lang.Long">
        SELECT count(1)
        FROM `resource`
        <choose>
            <when test="categoryIds != null and !categoryIds.isEmpty()">
                <choose>
                    <when test="categoryIds.get(0) == 0">
                        LEFT JOIN `resource_category` ON `resource_category`.`rid` = `resource`.`id`
                        WHERE `resource`.`is_hidden` = 0
                        AND `resource_category`.`cid` IS NULL
                    </when>
                    <otherwise>
                        INNER JOIN `resource_category` ON `resource_category`.`rid` = `resource`.`id`
                        WHERE `resource`.`is_hidden` = 0
                        AND `resource_category`.`cid` IN (<foreach collection="categoryIds" item="tmpId" separator=",">
                        #{tmpId}</foreach>)
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                WHERE `resource`.`is_hidden` = 0
            </otherwise>
        </choose>
        <if test="name != null and name != ''">
            AND `resource`.`name` LIKE concat('%',#{name},'%')
        </if>
        <if test="disk != null and disk != ''">
            AND `resource`.`disk` = #{disk}
        </if>
        <if test="extension != null and extension != ''">
            AND `resource`.`extension` = #{extension}
        </if>
        <if test="type != null and type != ''">
            AND `resource`.`type` IN
            <foreach item="item" collection="type.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="adminId != null and adminId != 0">
            AND `resource`.`admin_id` = #{adminId}
        </if>
    </select>

    <select id="paginateType" resultType="java.lang.String">
        SELECT DISTINCT ( `resource`.`type` )
        FROM `resource`
        <choose>
            <when test="categoryIds != null and !categoryIds.isEmpty()">
                <choose>
                    <when test="categoryIds.get(0) == 0">
                        LEFT JOIN `resource_category` ON `resource_category`.`rid` = `resource`.`id`
                        WHERE `resource`.`is_hidden` = 0
                        AND `resource_category`.`cid` IS NULL
                    </when>
                    <otherwise>
                        INNER JOIN `resource_category` ON `resource_category`.`rid` = `resource`.`id`
                        WHERE `resource`.`is_hidden` = 0
                        AND `resource_category`.`cid` IN (<foreach collection="categoryIds" item="tmpId" separator=",">
                        #{tmpId}</foreach>)
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                WHERE `resource`.`is_hidden` = 0
            </otherwise>
        </choose>
        <if test="name != null and name != ''">
            AND `resource`.`name` LIKE concat('%',#{name},'%')
        </if>
        <if test="disk != null and disk != ''">
            AND `resource`.`disk` = #{disk}
        </if>
        <if test="extension != null and extension != ''">
            AND `resource`.`extension` = #{extension}
        </if>
        <if test="type != null and type != ''">
            AND `resource`.`type` IN
            <foreach item="item" collection="type.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="adminId != null and adminId != 0">
            AND `resource`.`admin_id` = #{adminId}
        </if>
    </select>

</mapper>
