.audio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  width: 100%;

  .audio-card {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    :global(.ant-card-body) {
      padding: 16px;
    }

    :global(.ant-card-actions) {
      background: #fafafa;
      border-top: 1px solid #f0f0f0;

      li {
        margin: 8px 0;

        &:not(:last-child) {
          border-right: 1px solid #f0f0f0;
        }
      }
    }

    .audio-info {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 12px;

      .audio-icon {
        flex-shrink: 0;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f0f8ff;
        border-radius: 8px;
      }

      .audio-details {
        flex: 1;
        min-width: 0;

        h5 {
          margin-bottom: 8px;
          font-size: 14px;
          font-weight: 500;
        }

        :global(.ant-space-vertical) {
          width: 100%;

          .ant-space-item {
            font-size: 12px;
            line-height: 1.4;
          }
        }
      }
    }

    .audio-select {
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 10;

      :global(.ant-btn) {
        font-size: 12px;
        height: 28px;
        padding: 0 8px;
        border-radius: 4px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .audio-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .audio-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 8px;

    .audio-card {
      .audio-info {
        gap: 8px;

        .audio-icon {
          width: 40px;
          height: 40px;

          :global(.anticon) {
            font-size: 24px;
          }
        }

        .audio-details {
          h5 {
            font-size: 13px;
          }

          :global(.ant-space-vertical) {
            .ant-space-item {
              font-size: 11px;
            }
          }
        }
      }

      .audio-select {
        top: 6px;
        right: 6px;

        :global(.ant-btn) {
          font-size: 11px;
          height: 24px;
          padding: 0 6px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .audio-grid {
    grid-template-columns: 1fr;
    gap: 8px;

    .audio-card {
      .audio-info {
        .audio-details {
          h5 {
            font-size: 12px;
          }

          :global(.ant-space-vertical) {
            .ant-space-item {
              font-size: 10px;
            }
          }
        }
      }
    }
  }
}

// 播放状态样式
.audio-card.playing {
  border: 2px solid #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);

  .audio-icon {
    background: #1890ff !important;
    color: #fff !important;
  }
}

// 选中状态样式
.audio-card.selected {
  border: 2px solid #52c41a;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}
