<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.course.mapper.CourseMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.course.domain.Course">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="thumb" column="thumb" jdbcType="VARCHAR"/>
        <result property="shortDesc" column="short_desc" jdbcType="VARCHAR"/>
        <result property="charge" column="charge" jdbcType="INTEGER"/>
        <result property="classHour" column="class_hour" jdbcType="INTEGER"/>
        <result property="isShow" column="is_show" jdbcType="TINYINT"/>
        <result property="isRequired" column="is_required" jdbcType="TINYINT"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="sortAt" column="sort_at" jdbcType="TIMESTAMP"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
        <result property="deletedAt" column="deleted_at" jdbcType="TIMESTAMP"/>
        <result property="extra" column="extra" jdbcType="VARCHAR"/>
        <result property="adminId" column="admin_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,thumb,short_desc,
        charge,class_hour,is_show,
        is_required,created_at,updated_at,sort_at,
        deleted_at,extra,admin_id
    </sql>

    <select id="paginate" resultType="xyz.playedu.course.domain.Course">
        SELECT DISTINCT `courses`.*
        FROM `courses`
        <if test="depIds != null and !depIds.isEmpty()">
            <choose>
                <when test="depIds.get(0) == 0">
                    LEFT JOIN `course_department_user` ON `course_department_user`.`course_id` = `courses`.`id`
                </when>
                <otherwise>
                    INNER JOIN `course_department_user` ON `course_department_user`.`course_id` = `courses`.`id`
                </otherwise>
            </choose>
        </if>
        <if test="categoryIds != null and !categoryIds.isEmpty()">
            <choose>
                <when test="categoryIds.get(0) == 0">
                    LEFT JOIN `resource_course_category` ON `resource_course_category`.`course_id` = `courses`.`id`
                </when>
                <otherwise>
                    INNER JOIN `resource_course_category` ON `resource_course_category`.`course_id` = `courses`.`id`
                </otherwise>
            </choose>
        </if>

        <where>
            <if test="depIds != null and !depIds.isEmpty()">
                <choose>
                    <when test="depIds.get(0) == 0">
                        AND `course_department_user`.`course_id` IS NULL
                    </when>
                    <otherwise>
                        AND `course_department_user`.`range_id` IN (<foreach collection="depIds" item="tmpId" separator=",">
                        #{tmpId}</foreach>)
                    </otherwise>
                </choose>
            </if>
            <if test="categoryIds != null and !categoryIds.isEmpty()">
                <choose>
                    <when test="categoryIds.get(0) == 0">
                        AND `resource_course_category`.`course_id` IS NULL
                    </when>
                    <otherwise>
                        AND `resource_course_category`.`category_id` IN (<foreach collection="categoryIds" item="tmpId" separator=",">
                        #{tmpId}</foreach>)
                    </otherwise>
                </choose>
            </if>

            <if test="title != null and title != ''">
                AND `courses`.`title` LIKE concat('%',#{title},'%')
            </if>
            <if test="adminId != null">
                AND `courses`.`admin_id` = #{adminId}
            </if>
            <if test="isRequired != null">
                AND `courses`.`is_required` = #{isRequired}
            </if>
        </where>

        <if test="sortAlgo == 'asc'">
            <choose>
                <when test="sortField == 'charge'">
                    ORDER BY `courses`.`charge` ASC
                </when>
                <when test="sortField == 'class_hour'">
                    ORDER BY `courses`.`class_hour` ASC
                </when>
                <when test="sortField == 'created_at'">
                    ORDER BY `courses`.`created_at` ASC
                </when>
                <otherwise>
                    ORDER BY `courses`.`id` ASC
                </otherwise>
            </choose>
        </if>
        <if test="sortAlgo != 'asc'">
            <choose>
                <when test="sortField == 'charge'">
                    ORDER BY `courses`.`charge` DESC
                </when>
                <when test="sortField == 'class_hour'">
                    ORDER BY `courses`.`class_hour` DESC
                </when>
                <when test="sortField == 'created_at'">
                    ORDER BY `courses`.`created_at` DESC
                </when>
                <otherwise>
                    ORDER BY `courses`.`id` DESC
                </otherwise>
            </choose>
        </if>
        LIMIT #{pageStart}, #{pageSize};
    </select>

    <select id="paginateCount" resultType="java.lang.Long">
        SELECT count(1) FROM (
        SELECT DISTINCT `courses`.*
        FROM `courses`
        <if test="depIds != null and !depIds.isEmpty()">
            <choose>
                <when test="depIds.get(0) == 0">
                    LEFT JOIN `course_department_user` ON `course_department_user`.`course_id` = `courses`.`id`
                </when>
                <otherwise>
                    INNER JOIN `course_department_user` ON `course_department_user`.`course_id` = `courses`.`id`
                </otherwise>
            </choose>
        </if>
        <if test="categoryIds != null and !categoryIds.isEmpty()">
            <choose>
                <when test="categoryIds.get(0) == 0">
                    LEFT JOIN `resource_course_category` ON `resource_course_category`.`course_id` = `courses`.`id`
                </when>
                <otherwise>
                    INNER JOIN `resource_course_category` ON `resource_course_category`.`course_id` = `courses`.`id`
                </otherwise>
            </choose>
        </if>

        <where>
            <if test="depIds != null and !depIds.isEmpty()">
                <choose>
                    <when test="depIds.get(0) == 0">
                        AND `course_department_user`.`course_id` IS NULL
                    </when>
                    <otherwise>
                        AND `course_department_user`.`range_id` IN (<foreach collection="depIds" item="tmpId" separator=",">
                        #{tmpId}</foreach>)
                    </otherwise>
                </choose>
            </if>
            <if test="categoryIds != null and !categoryIds.isEmpty()">
                <choose>
                    <when test="categoryIds.get(0) == 0">
                        AND `resource_course_category`.`course_id` IS NULL
                    </when>
                    <otherwise>
                        AND `resource_course_category`.`category_id` IN (<foreach collection="categoryIds" item="tmpId"
                                                                                  separator=",">
                        #{tmpId}</foreach>)
                    </otherwise>
                </choose>
            </if>

            <if test="title != null and title != ''">
                AND `courses`.`title` LIKE concat('%',#{title},'%')
            </if>
            <if test="adminId != null">
                AND `courses`.`admin_id` = #{adminId}
            </if>
            <if test="isRequired != null">
                AND `courses`.`is_required` = #{isRequired}
            </if>
        </where>
        ) m
    </select>
    <select id="openCoursesAndShow" resultType="xyz.playedu.course.domain.Course">
        SELECT `courses`.*
        FROM `courses`
        LEFT JOIN `course_department_user` ON `course_department_user`.`course_id` = `courses`.`id`
        <if test="categoryIds != null and !categoryIds.isEmpty()">
            INNER JOIN `resource_course_category` ON `resource_course_category`.`course_id` = `courses`.`id`
        </if>
        WHERE `course_department_user`.`course_id` IS NULL
        AND `courses`.`is_show` = 1
        <if test="categoryIds != null and !categoryIds.isEmpty()">
            AND `resource_course_category`.`category_id` IN (<foreach collection="categoryIds" item="tmpId"
                                                                      separator=",">
            #{tmpId}</foreach>)
        </if>
        LIMIT #{limit}
    </select>
</mapper>
