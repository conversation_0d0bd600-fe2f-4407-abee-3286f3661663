import { useState, useEffect } from "react";
import { Modal, Form, Input, Button, Space, message, Progress, Select } from "antd";
import { UploadOutlined, SoundOutlined } from "@ant-design/icons";
import { upload, resourceCategory } from "../../api/index";
import styles from "./index.module.less";

const { Option } = Select;

interface PropInterface {
  categoryIds: number[];
  onUpdate: () => void;
}

export const UploadAudioSub: React.FC<PropInterface> = ({
  categoryIds,
  onUpdate,
}) => {
  const [form] = Form.useForm();
  const [showModal, setShowModal] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState<boolean>(false);

  useEffect(() => {
    if (showModal) {
      getCategories();
      form.setFieldsValue({
        category_ids: categoryIds,
      });
    }
  }, [showModal, categoryIds]);

  const getCategories = () => {
    resourceCategory.resourceCategoryList().then((res: any) => {
      const categoryList = transformCategories(res.data.categories);
      setCategories(categoryList);
    });
  };

  const transformCategories = (categories: any, parentId = 0, level = 0): any[] => {
    const result: any[] = [];
    if (categories[parentId]) {
      categories[parentId].forEach((item: any) => {
        result.push({
          ...item,
          level,
          label: "　".repeat(level) + item.name,
        });
        result.push(...transformCategories(categories, item.id, level + 1));
      });
    }
    return result;
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 检查文件类型
    const allowedTypes = ["audio/mpeg", "audio/wav", "audio/mp4", "audio/aac", "audio/flac"];
    const allowedExtensions = ["mp3", "wav", "m4a", "aac", "flac"];
    const fileExtension = file.name.split(".").pop()?.toLowerCase();

    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension || "")) {
      message.error("只支持 MP3、WAV、M4A、AAC、FLAC 格式的音频文件");
      return;
    }

    // 检查文件大小 (限制为100MB)
    if (file.size > 100 * 1024 * 1024) {
      message.error("音频文件大小不能超过100MB");
      return;
    }

    setSelectedFile(file);
    form.setFieldsValue({
      name: file.name.replace(/\.[^/.]+$/, ""), // 去掉扩展名
    });
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      message.error("请选择音频文件");
      return;
    }

    try {
      await form.validateFields();
      const values = form.getFieldsValue();

      setIsUploading(true);
      setUploadProgress(0);

      // 创建FormData
      const formData = new FormData();
      formData.append("file", selectedFile);
      formData.append("name", values.name);
      formData.append("category_ids", values.category_ids.join(","));

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + Math.random() * 10;
        });
      }, 200);

      // 执行上传
      const response = await upload.uploadAudio(formData);
      
      clearInterval(progressInterval);
      setUploadProgress(100);

      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
        setSelectedFile(null);
        setShowModal(false);
        form.resetFields();
        message.success("音频上传成功");
        onUpdate();
      }, 500);

    } catch (error) {
      setIsUploading(false);
      setUploadProgress(0);
      message.error("上传失败，请重试");
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <>
      <Button
        type="primary"
        icon={<UploadOutlined />}
        onClick={() => setShowModal(true)}
      >
        上传音频
      </Button>

      <Modal
        title="上传音频文件"
        open={showModal}
        onCancel={() => {
          if (!isUploading) {
            setShowModal(false);
            setSelectedFile(null);
            form.resetFields();
          }
        }}
        footer={null}
        width={600}
        maskClosable={!isUploading}
        closable={!isUploading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpload}
        >
          <Form.Item
            label="选择音频文件"
            required
          >
            <div className={styles["upload-area"]}>
              <input
                type="file"
                accept="audio/*,.mp3,.wav,.m4a,.aac,.flac"
                onChange={handleFileSelect}
                style={{ display: "none" }}
                id="audio-upload-input"
                disabled={isUploading}
              />
              <label
                htmlFor="audio-upload-input"
                className={styles["upload-label"]}
              >
                <div className={styles["upload-content"]}>
                  <SoundOutlined style={{ fontSize: "48px", color: "#1890ff" }} />
                  <div className={styles["upload-text"]}>
                    <div>点击选择音频文件</div>
                    <div className={styles["upload-hint"]}>
                      支持 MP3、WAV、M4A、AAC、FLAC 格式，文件大小不超过100MB
                    </div>
                  </div>
                </div>
              </label>
            </div>

            {selectedFile && (
              <div className={styles["file-info"]}>
                <div className={styles["file-details"]}>
                  <SoundOutlined style={{ color: "#1890ff", marginRight: "8px" }} />
                  <span className={styles["file-name"]}>{selectedFile.name}</span>
                  <span className={styles["file-size"]}>
                    ({formatFileSize(selectedFile.size)})
                  </span>
                </div>
              </div>
            )}

            {isUploading && (
              <div className={styles["upload-progress"]}>
                <Progress
                  percent={Math.round(uploadProgress)}
                  status={uploadProgress === 100 ? "success" : "active"}
                  strokeColor={{
                    "0%": "#108ee9",
                    "100%": "#87d068",
                  }}
                />
                <div className={styles["progress-text"]}>
                  {uploadProgress < 100 ? "正在上传..." : "上传完成"}
                </div>
              </div>
            )}
          </Form.Item>

          <Form.Item
            label="音频名称"
            name="name"
            rules={[{ required: true, message: "请输入音频名称" }]}
          >
            <Input
              placeholder="请输入音频名称"
              disabled={isUploading}
            />
          </Form.Item>

          <Form.Item
            label="所属分类"
            name="category_ids"
            rules={[{ required: true, message: "请选择所属分类" }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择所属分类"
              disabled={isUploading}
            >
              {categories.map((item) => (
                <Option key={item.id} value={item.id}>
                  {item.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={isUploading}
                disabled={!selectedFile}
              >
                {isUploading ? "上传中..." : "开始上传"}
              </Button>
              <Button
                onClick={() => {
                  if (!isUploading) {
                    setShowModal(false);
                    setSelectedFile(null);
                    form.resetFields();
                  }
                }}
                disabled={isUploading}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
