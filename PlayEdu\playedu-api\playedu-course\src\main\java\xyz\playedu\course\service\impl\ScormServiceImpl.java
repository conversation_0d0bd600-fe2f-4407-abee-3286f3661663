/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.course.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import xyz.playedu.course.domain.ScormAttempt;
import xyz.playedu.course.domain.ScormPackage;
import xyz.playedu.course.mapper.ScormAttemptMapper;
import xyz.playedu.course.mapper.ScormPackageMapper;
import xyz.playedu.course.service.ScormService;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * SCORM服务实现
 * 
 * <AUTHOR> Team
 */
@Service
@Slf4j
public class ScormServiceImpl implements ScormService {

    @Autowired
    private ScormPackageMapper scormPackageMapper;

    @Autowired
    private ScormAttemptMapper scormAttemptMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // SCORM 1.2 数据模型
    private static final Map<String, String> SCORM_12_ELEMENTS = new HashMap<>();
    // SCORM 2004 数据模型
    private static final Map<String, String> SCORM_2004_ELEMENTS = new HashMap<>();

    static {
        // SCORM 1.2 核心元素
        SCORM_12_ELEMENTS.put("cmi.core.student_id", "");
        SCORM_12_ELEMENTS.put("cmi.core.student_name", "");
        SCORM_12_ELEMENTS.put("cmi.core.lesson_location", "");
        SCORM_12_ELEMENTS.put("cmi.core.lesson_status", "not attempted");
        SCORM_12_ELEMENTS.put("cmi.core.score.raw", "");
        SCORM_12_ELEMENTS.put("cmi.core.score.max", "");
        SCORM_12_ELEMENTS.put("cmi.core.score.min", "");
        SCORM_12_ELEMENTS.put("cmi.core.total_time", "0000:00:00.00");
        SCORM_12_ELEMENTS.put("cmi.core.session_time", "");
        SCORM_12_ELEMENTS.put("cmi.core.exit", "");
        SCORM_12_ELEMENTS.put("cmi.core.entry", "ab-initio");
        SCORM_12_ELEMENTS.put("cmi.suspend_data", "");

        // SCORM 2004 核心元素
        SCORM_2004_ELEMENTS.put("cmi.learner_id", "");
        SCORM_2004_ELEMENTS.put("cmi.learner_name", "");
        SCORM_2004_ELEMENTS.put("cmi.location", "");
        SCORM_2004_ELEMENTS.put("cmi.completion_status", "not attempted");
        SCORM_2004_ELEMENTS.put("cmi.success_status", "unknown");
        SCORM_2004_ELEMENTS.put("cmi.score.raw", "");
        SCORM_2004_ELEMENTS.put("cmi.score.max", "");
        SCORM_2004_ELEMENTS.put("cmi.score.min", "");
        SCORM_2004_ELEMENTS.put("cmi.score.scaled", "");
        SCORM_2004_ELEMENTS.put("cmi.total_time", "PT0H0M0S");
        SCORM_2004_ELEMENTS.put("cmi.session_time", "");
        SCORM_2004_ELEMENTS.put("cmi.exit", "");
        SCORM_2004_ELEMENTS.put("cmi.entry", "ab-initio");
        SCORM_2004_ELEMENTS.put("cmi.mode", "normal");
        SCORM_2004_ELEMENTS.put("cmi.credit", "credit");
        SCORM_2004_ELEMENTS.put("cmi.suspend_data", "");
        SCORM_2004_ELEMENTS.put("cmi.progress_measure", "");
    }

    @Override
    public ScormPackage parseScormPackage(Integer resourceId, String zipFilePath) {
        try {
            // 创建解压目录
            String extractPath = createExtractPath(resourceId);
            
            // 解压ZIP文件
            extractZipFile(zipFilePath, extractPath);
            
            // 读取manifest.xml
            String manifestPath = extractPath + "/imsmanifest.xml";
            String manifestContent = readManifestFile(manifestPath);
            
            if (manifestContent == null) {
                throw new RuntimeException("未找到imsmanifest.xml文件");
            }

            // 解析manifest.xml
            Document doc = parseXmlDocument(manifestContent);
            
            // 创建SCORM包记录
            ScormPackage scormPackage = new ScormPackage();
            scormPackage.setResourceId(resourceId);
            scormPackage.setExtractPath(extractPath);
            scormPackage.setManifestContent(manifestContent);
            
            // 解析基本信息
            parseBasicInfo(doc, scormPackage);
            
            // 解析组织结构
            String organizationJson = parseOrganization(manifestContent);
            scormPackage.setOrganizationJson(organizationJson);
            
            // 解析资源清单
            String resourcesJson = parseResources(manifestContent);
            scormPackage.setResourcesJson(resourcesJson);
            
            // 获取启动文件
            String launchFile = getLaunchFile(manifestContent);
            scormPackage.setLaunchFile(launchFile);
            
            // 获取SCORM版本
            String scormVersion = getScormVersion(manifestContent);
            scormPackage.setScormVersion(scormVersion);
            
            scormPackage.setParseStatus(1); // 解析成功
            scormPackage.setCreatedAt(new Date());
            scormPackage.setUpdatedAt(new Date());
            
            // 保存到数据库
            scormPackageMapper.insert(scormPackage);
            
            return scormPackage;
            
        } catch (Exception e) {
            log.error("SCORM包解析失败", e);
            
            // 创建失败记录
            ScormPackage scormPackage = new ScormPackage();
            scormPackage.setResourceId(resourceId);
            scormPackage.setParseStatus(2); // 解析失败
            scormPackage.setParseError(e.getMessage());
            scormPackage.setCreatedAt(new Date());
            scormPackage.setUpdatedAt(new Date());
            
            scormPackageMapper.insert(scormPackage);
            
            throw new RuntimeException("SCORM包解析失败: " + e.getMessage());
        }
    }

    @Override
    public ScormPackage getScormPackage(Integer resourceId) {
        QueryWrapper<ScormPackage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("resource_id", resourceId);
        return scormPackageMapper.selectOne(queryWrapper);
    }

    @Override
    public ScormAttempt createAttempt(Integer userId, Integer packageId, Integer hourId) {
        // 获取用户的尝试次数
        QueryWrapper<ScormAttempt> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("package_id", packageId);
        Long attemptCount = scormAttemptMapper.selectCount(queryWrapper);
        
        ScormAttempt attempt = new ScormAttempt();
        attempt.setUserId(userId);
        attempt.setPackageId(packageId);
        attempt.setHourId(hourId);
        attempt.setAttemptNumber(attemptCount.intValue() + 1);
        attempt.setLessonStatus("not attempted");
        attempt.setCompletionStatus("not attempted");
        attempt.setSuccessStatus("unknown");
        attempt.setEntry("ab-initio");
        attempt.setMode("normal");
        attempt.setCredit("credit");
        attempt.setStartTime(new Date());
        attempt.setCreatedAt(new Date());
        attempt.setUpdatedAt(new Date());
        
        scormAttemptMapper.insert(attempt);
        return attempt;
    }

    @Override
    public ScormAttempt getCurrentAttempt(Integer userId, Integer packageId) {
        QueryWrapper<ScormAttempt> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("package_id", packageId)
                   .orderByDesc("attempt_number")
                   .last("LIMIT 1");
        return scormAttemptMapper.selectOne(queryWrapper);
    }

    @Override
    public Map<String, Object> initializeRuntime(Integer attemptId) {
        ScormAttempt attempt = scormAttemptMapper.selectById(attemptId);
        if (attempt == null) {
            throw new RuntimeException("学习尝试不存在");
        }

        ScormPackage scormPackage = scormPackageMapper.selectById(attempt.getPackageId());
        if (scormPackage == null) {
            throw new RuntimeException("SCORM包不存在");
        }

        Map<String, Object> runtime = new HashMap<>();
        runtime.put("attemptId", attemptId);
        runtime.put("scormVersion", scormPackage.getScormVersion());
        runtime.put("launchFile", scormPackage.getLaunchFile());
        runtime.put("extractPath", scormPackage.getExtractPath());
        
        // 初始化数据模型
        Map<String, String> dataModel = new HashMap<>();
        if ("1.2".equals(scormPackage.getScormVersion())) {
            dataModel.putAll(SCORM_12_ELEMENTS);
        } else {
            dataModel.putAll(SCORM_2004_ELEMENTS);
        }
        
        runtime.put("dataModel", dataModel);
        
        return runtime;
    }

    // 其他方法实现...
    @Override
    public String getValue(Integer attemptId, String element) {
        // 实现获取数据模型值的逻辑
        return "";
    }

    @Override
    public boolean setValue(Integer attemptId, String element, String value) {
        // 实现设置数据模型值的逻辑
        return true;
    }

    @Override
    public boolean commit(Integer attemptId) {
        // 实现提交数据的逻辑
        return true;
    }

    @Override
    public boolean terminate(Integer attemptId) {
        // 实现终止会话的逻辑
        return true;
    }

    @Override
    public String getLastError(Integer attemptId) {
        return "0"; // 无错误
    }

    @Override
    public String getErrorString(Integer attemptId, String errorCode) {
        return "";
    }

    @Override
    public String getDiagnostic(Integer attemptId, String errorCode) {
        return "";
    }

    // 私有辅助方法
    private String createExtractPath(Integer resourceId) {
        return System.getProperty("java.io.tmpdir") + "/scorm/packages/" + resourceId;
    }

    private void extractZipFile(String zipFilePath, String extractPath) throws IOException {
        Path extractDir = Paths.get(extractPath);
        Files.createDirectories(extractDir);

        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFilePath))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                Path entryPath = extractDir.resolve(entry.getName());

                // 安全检查：防止目录遍历攻击
                if (!entryPath.startsWith(extractDir)) {
                    throw new IOException("Entry is outside of the target dir: " + entry.getName());
                }

                if (entry.isDirectory()) {
                    Files.createDirectories(entryPath);
                } else {
                    Files.createDirectories(entryPath.getParent());
                    Files.copy(zis, entryPath);
                }
                zis.closeEntry();
            }
        }
    }

    private String readManifestFile(String manifestPath) throws IOException {
        Path path = Paths.get(manifestPath);
        if (!Files.exists(path)) {
            return null;
        }
        return new String(Files.readAllBytes(path), "UTF-8");
    }

    private Document parseXmlDocument(String xmlContent) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setNamespaceAware(true);
        DocumentBuilder builder = factory.newDocumentBuilder();
        return builder.parse(new ByteArrayInputStream(xmlContent.getBytes("UTF-8")));
    }

    private void parseBasicInfo(Document doc, ScormPackage scormPackage) {
        Element root = doc.getDocumentElement();

        // 解析基本信息
        scormPackage.setIdentifier(root.getAttribute("identifier"));
        scormPackage.setVersion(root.getAttribute("version"));

        // 解析标题
        NodeList titleNodes = root.getElementsByTagName("title");
        if (titleNodes.getLength() > 0) {
            scormPackage.setTitle(titleNodes.item(0).getTextContent());
        }

        // 解析描述
        NodeList descNodes = root.getElementsByTagName("description");
        if (descNodes.getLength() > 0) {
            scormPackage.setDescription(descNodes.item(0).getTextContent());
        }
    }

    @Override
    public boolean validateScormPackage(String zipFilePath) {
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFilePath))) {
            ZipEntry entry;
            boolean hasManifest = false;

            while ((entry = zis.getNextEntry()) != null) {
                if ("imsmanifest.xml".equals(entry.getName())) {
                    hasManifest = true;
                    break;
                }
                zis.closeEntry();
            }

            return hasManifest;
        } catch (IOException e) {
            log.error("验证SCORM包失败", e);
            return false;
        }
    }

    @Override
    public String getScormVersion(String manifestContent) {
        try {
            Document doc = parseXmlDocument(manifestContent);
            NodeList metadataNodes = doc.getElementsByTagName("schemaversion");

            if (metadataNodes.getLength() > 0) {
                String version = metadataNodes.item(0).getTextContent();
                if (version.contains("1.2")) {
                    return "1.2";
                } else if (version.contains("2004")) {
                    return "2004";
                }
            }

            // 默认返回1.2
            return "1.2";
        } catch (Exception e) {
            log.error("解析SCORM版本失败", e);
            return "1.2";
        }
    }

    @Override
    public String parseOrganization(String manifestContent) {
        try {
            Document doc = parseXmlDocument(manifestContent);
            NodeList orgNodes = doc.getElementsByTagName("organization");

            Map<String, Object> organization = new HashMap<>();
            if (orgNodes.getLength() > 0) {
                Element orgElement = (Element) orgNodes.item(0);
                organization.put("identifier", orgElement.getAttribute("identifier"));

                NodeList titleNodes = orgElement.getElementsByTagName("title");
                if (titleNodes.getLength() > 0) {
                    organization.put("title", titleNodes.item(0).getTextContent());
                }

                // 解析项目结构
                NodeList itemNodes = orgElement.getElementsByTagName("item");
                List<Map<String, Object>> items = new ArrayList<>();

                for (int i = 0; i < itemNodes.getLength(); i++) {
                    Element itemElement = (Element) itemNodes.item(i);
                    Map<String, Object> item = new HashMap<>();
                    item.put("identifier", itemElement.getAttribute("identifier"));
                    item.put("identifierref", itemElement.getAttribute("identifierref"));

                    NodeList itemTitleNodes = itemElement.getElementsByTagName("title");
                    if (itemTitleNodes.getLength() > 0) {
                        item.put("title", itemTitleNodes.item(0).getTextContent());
                    }

                    items.add(item);
                }

                organization.put("items", items);
            }

            return objectMapper.writeValueAsString(organization);
        } catch (Exception e) {
            log.error("解析组织结构失败", e);
            return "{}";
        }
    }

    @Override
    public String parseResources(String manifestContent) {
        try {
            Document doc = parseXmlDocument(manifestContent);
            NodeList resourceNodes = doc.getElementsByTagName("resource");

            List<Map<String, Object>> resources = new ArrayList<>();

            for (int i = 0; i < resourceNodes.getLength(); i++) {
                Element resourceElement = (Element) resourceNodes.item(i);
                Map<String, Object> resource = new HashMap<>();

                resource.put("identifier", resourceElement.getAttribute("identifier"));
                resource.put("type", resourceElement.getAttribute("type"));
                resource.put("href", resourceElement.getAttribute("href"));

                // 解析文件列表
                NodeList fileNodes = resourceElement.getElementsByTagName("file");
                List<String> files = new ArrayList<>();

                for (int j = 0; j < fileNodes.getLength(); j++) {
                    Element fileElement = (Element) fileNodes.item(j);
                    files.add(fileElement.getAttribute("href"));
                }

                resource.put("files", files);
                resources.add(resource);
            }

            return objectMapper.writeValueAsString(resources);
        } catch (Exception e) {
            log.error("解析资源清单失败", e);
            return "{}";
        }
    }

    @Override
    public String getLaunchFile(String manifestContent) {
        try {
            Document doc = parseXmlDocument(manifestContent);

            // 首先查找默认组织的第一个项目
            NodeList orgNodes = doc.getElementsByTagName("organization");
            if (orgNodes.getLength() > 0) {
                Element orgElement = (Element) orgNodes.item(0);
                NodeList itemNodes = orgElement.getElementsByTagName("item");

                if (itemNodes.getLength() > 0) {
                    Element itemElement = (Element) itemNodes.item(0);
                    String identifierref = itemElement.getAttribute("identifierref");

                    // 根据identifierref查找对应的资源
                    NodeList resourceNodes = doc.getElementsByTagName("resource");
                    for (int i = 0; i < resourceNodes.getLength(); i++) {
                        Element resourceElement = (Element) resourceNodes.item(i);
                        if (identifierref.equals(resourceElement.getAttribute("identifier"))) {
                            String href = resourceElement.getAttribute("href");
                            return href.isEmpty() ? "index.html" : href;
                        }
                    }
                }
            }

            return "index.html";
        } catch (Exception e) {
            log.error("获取启动文件失败", e);
            return "index.html";
        }
    }
}
