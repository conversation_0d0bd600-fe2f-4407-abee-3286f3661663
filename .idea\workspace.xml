<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="77d49b78-0541-4089-8bda-8e5e8ebb337c" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/PlayEdu/docs/create.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/docs/data.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/docs/drop_database.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/docs/scorm实现.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/docs/高级学习内容支持.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/public/scorm-api.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/public/scorm-simple-test.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/public/scorm-test.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/public/test-scorm-api.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/api/live.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/api/scorm.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/compenents/scorm-player/index.module.less" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/compenents/scorm-player/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/compenents/upload-audio-button/index.module.less" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/compenents/upload-audio-button/upload-audio-sub.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/compenents/upload-scorm-button/index.module.less" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/compenents/upload-scorm-button/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/compenents/upload-scorm-button/upload-scorm-sub.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/pages/live/sessions/components/create.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/pages/live/sessions/components/update.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/pages/live/sessions/index.module.less" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/pages/live/sessions/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/pages/resource/audios/index.module.less" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/pages/resource/audios/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/pages/resource/scorm/index.module.less" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/pages/resource/scorm/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/pages/resource/scorm/preview.module.less" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/pages/resource/scorm/preview.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-api/src/main/java/xyz/playedu/api/config/AsyncConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-api/src/main/java/xyz/playedu/api/controller/backend/LiveController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-api/src/main/java/xyz/playedu/api/controller/backend/ScormController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-api/src/main/java/xyz/playedu/api/controller/frontend/ScormApiController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-api/src/main/java/xyz/playedu/api/controller/frontend/ScormFileController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/domain/LiveSession.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/domain/ScormAttempt.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/domain/ScormPackage.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/mapper/ScormPackageMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/request/LiveSessionRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/service/LiveSessionService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/service/ScormAttemptService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/service/ScormPackageService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/service/ScormParseService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/service/ScormService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/service/impl/LiveSessionServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/service/impl/ScormAttemptServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/service/impl/ScormPackageServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/service/impl/ScormParseServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/service/impl/ScormServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-resource/src/main/java/xyz/playedu/resource/service/DocumentPreviewService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-resource/src/main/java/xyz/playedu/resource/service/impl/DocumentPreviewServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-pc/public/pdf-viewer.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-pc/src/components/audio-player/index.module.less" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-pc/src/components/audio-player/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-pc/src/components/document-viewer/index.module.less" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-pc/src/components/document-viewer/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-pc/src/components/live-player/index.module.less" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-pc/src/components/live-player/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-pc/src/components/scorm-player/index.module.less" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-pc/src/components/scorm-player/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-pc/src/pages/course/media-player.module.scss" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/PlayEdu/playedu-pc/src/pages/course/media-player.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/api/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/api/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/api/internal/httpClient.ts" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/api/internal/httpClient.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/api/upload.ts" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/api/upload.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/compenents/left-menu/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/compenents/left-menu/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/routes/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-admin/src/routes/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-api/src/main/java/xyz/playedu/api/controller/backend/UploadController.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-api/src/main/java/xyz/playedu/api/controller/backend/UploadController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-api/src/main/java/xyz/playedu/api/controller/frontend/HourController.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-api/src/main/java/xyz/playedu/api/controller/frontend/HourController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-api/src/main/java/xyz/playedu/api/interceptor/WebMvcConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-api/src/main/java/xyz/playedu/api/interceptor/WebMvcConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-api/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-api/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/constant/BPermissionConstant.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/constant/BPermissionConstant.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/constant/BackendConstant.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/constant/BackendConstant.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/constant/ConfigConstant.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/constant/ConfigConstant.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/service/AdminPermissionService.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/service/AdminPermissionService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/service/AdminRolePermissionService.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/service/AdminRolePermissionService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/service/impl/AdminPermissionServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/service/impl/AdminPermissionServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/service/impl/AdminRolePermissionServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/service/impl/AdminRolePermissionServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/service/impl/AdminUserRoleServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/service/impl/AdminUserRoleServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/service/impl/UserServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/service/impl/UserServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/util/HelperUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/util/HelperUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/util/IpUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/util/IpUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/util/S3Util.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-common/src/main/java/xyz/playedu/common/util/S3Util.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-system/src/main/java/xyz/playedu/system/checks/AdminPermissionCheck.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-system/src/main/java/xyz/playedu/system/checks/AppConfigCheck.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-system/src/main/java/xyz/playedu/system/checks/MigrationCheck.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-system/src/main/java/xyz/playedu/system/checks/SystemDataCheck.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-system/src/main/java/xyz/playedu/system/checks/UpgradeCheck.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-system/src/main/java/xyz/playedu/system/domain/Migration.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-system/src/main/java/xyz/playedu/system/mapper/MigrationMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/mapper/LiveSessionMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-system/src/main/java/xyz/playedu/system/service/MigrationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-course/src/main/java/xyz/playedu/course/mapper/ScormAttemptMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-system/src/main/java/xyz/playedu/system/service/impl/MigrationServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/playedu-system/src/main/resources/mapper/MigrationMapper.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-api/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-api/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/PlayEdu/playedu-pc/src/routes/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/PlayEdu/playedu-pc/src/routes/index.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/PlayEdu" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\opensource\maven\apache-maven-3.9.9" />
        <option name="localRepository" value="D:\opensource\maven\apache-maven-3.9.9\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\opensource\maven\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="30oSXwxGSl9FhcHSuB0TIPCIyia" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.playedu [clean].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.PlayeduApiApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/code/learn/playedu&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDK&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\code\\learn\\playedu\\PlayEdu\\playedu-admin\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\code\learn\playedu\PlayEdu\playedu-api\playedu-course\src\main\java\xyz\playedu\course\mapper" />
      <recent name="D:\code\learn\playedu\PlayEdu\docs" />
      <recent name="D:\code\learn\playedu\PlayEdu\playedu-pc\src\pages\course" />
      <recent name="D:\code\learn\playedu\PlayEdu\playedu-pc\src\pages\course\compenents" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="PlayeduApiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="playedu-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="xyz.playedu.api.PlayeduApiApplication" />
      <option name="VM_PARAMETERS" value="-Dspring.profiles.active=dev" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="77d49b78-0541-4089-8bda-8e5e8ebb337c" name="更改" comment="" />
      <created>1754292476172</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754292476172</updated>
      <workItem from="1754292477493" duration="21054000" />
      <workItem from="1754358685876" duration="3320000" />
      <workItem from="1754363092378" duration="613000" />
      <workItem from="1754363730518" duration="13773000" />
      <workItem from="1754378314788" duration="21192000" />
      <workItem from="1754441550679" duration="2719000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>