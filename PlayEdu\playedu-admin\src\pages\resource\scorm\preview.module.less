.container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 10;
}

.backButton {
  margin-right: 16px;
  color: #666;
  
  &:hover {
    color: #1890ff;
    background: rgba(24, 144, 255, 0.06);
  }
}

.titleInfo {
  display: flex;
  align-items: center;
  gap: 12px;
  
  h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #262626;
  }
}

.version {
  padding: 2px 8px;
  background: #f0f0f0;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.content {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

.scormFrame {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: #fff;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.errorContainer {
  padding: 24px;
  max-width: 600px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 12px 16px;
    
    .titleInfo h1 {
      font-size: 16px;
    }
  }
  
  .content {
    padding: 12px;
  }
  
  .backButton {
    margin-right: 8px;
  }
}
