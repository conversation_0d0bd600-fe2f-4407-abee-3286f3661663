/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.controller.frontend;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import xyz.playedu.common.exception.ServiceException;
import xyz.playedu.common.types.JsonResponse;
import xyz.playedu.course.domain.ScormAttempt;
import xyz.playedu.course.domain.ScormPackage;
import xyz.playedu.course.service.ScormAttemptService;
import xyz.playedu.course.service.ScormPackageService;

import java.util.Date;

import java.util.HashMap;
import java.util.Map;

/**
 * SCORM API控制器 - 实现SCORM运行时API
 *
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/api/v1/scorm")
@Slf4j
public class ScormApiController {

    @Autowired
    private ScormPackageService scormPackageService;

    @Autowired
    private ScormAttemptService scormAttemptService;

    /**
     * 初始化SCORM会话
     */
    @PostMapping("/packages/{packageId}/initialize")
    public JsonResponse initialize(@PathVariable Integer packageId,
                                 @RequestParam(required = false) Integer hourId,
                                  @RequestParam(required = false, defaultValue = "1") Integer userId) throws ServiceException {
        
        ScormPackage scormPackage = scormPackageService.getById(packageId);
        if (scormPackage == null) {
            throw new ServiceException("SCORM包不存在");
        }

        if (scormPackage.getParseStatus() != 1) {
            throw new ServiceException("SCORM包尚未解析完成");
        }

        // 创建或获取学习尝试
        ScormAttempt attempt = scormAttemptService.getCurrentAttempt(userId, packageId);
        if (attempt == null) {
            attempt = scormAttemptService.createAttempt(userId, packageId, hourId);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("attempt_id", attempt.getId());
        data.put("package_info", scormPackage);
        data.put("api_url", "/api/v1/scorm/attempts/" + attempt.getId());

        log.info("SCORM会话初始化成功: attemptId={}", attempt.getId());
        return JsonResponse.data(data);
    }

    /**
     * 测试SCORM API连接
     */
    @GetMapping("/test")
    public JsonResponse test() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "ok");
        data.put("message", "SCORM API is working");
        data.put("timestamp", new Date());
        return JsonResponse.data(data);
    }

    /**
     * 测试SCORM包查询
     */
    @GetMapping("/packages/{packageId}/test")
    public JsonResponse testPackage(@PathVariable Integer packageId) {
        try {
            ScormPackage scormPackage = scormPackageService.getById(packageId);
            if (scormPackage == null) {
                return JsonResponse.error("SCORM包不存在");
            }

            Map<String, Object> data = new HashMap<>();
            data.put("package_id", scormPackage.getId());
            data.put("title", scormPackage.getTitle());
            data.put("parse_status", scormPackage.getParseStatus());
            data.put("message", "SCORM包查询成功");

            return JsonResponse.data(data);
        } catch (Exception e) {
            log.error("测试SCORM包查询失败", e);
            return JsonResponse.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取SCORM数据
     */
    @GetMapping("/attempts/{attemptId}/data")
    public JsonResponse getData(@PathVariable Integer attemptId,
                               @RequestParam String element,
                               @RequestParam(required = false, defaultValue = "1") Integer userId) throws ServiceException {

        ScormAttempt attempt = scormAttemptService.getById(attemptId);
        if (attempt == null || !attempt.getUserId().equals(userId)) {
            throw new ServiceException("学习记录不存在");
        }

        String value = getScormValue(attempt, element);
        
        Map<String, Object> data = new HashMap<>();
        data.put("element", element);
        data.put("value", value);
        data.put("error_code", "0");

        return JsonResponse.data(data);
    }

    /**
     * 设置SCORM数据
     */
    @PostMapping("/attempts/{attemptId}/data")
    public JsonResponse setData(@PathVariable Integer attemptId,
                               @RequestParam String element,
                               @RequestParam String value,
                               @RequestParam(required = false, defaultValue = "1") Integer userId) throws ServiceException {

        ScormAttempt attempt = scormAttemptService.getById(attemptId);
        if (attempt == null || !attempt.getUserId().equals(userId)) {
            throw new ServiceException("学习记录不存在");
        }

        boolean success = scormAttemptService.updateProgress(attemptId, element, value);
        
        Map<String, Object> data = new HashMap<>();
        data.put("success", success);
        data.put("error_code", success ? "0" : "101");

        return JsonResponse.data(data);
    }

    /**
     * 提交SCORM数据
     */
    @PostMapping("/attempts/{attemptId}/commit")
    public JsonResponse commit(@PathVariable Integer attemptId,
                              @RequestParam(required = false, defaultValue = "1") Integer userId) throws ServiceException {

        ScormAttempt attempt = scormAttemptService.getById(attemptId);
        if (attempt == null || !attempt.getUserId().equals(userId)) {
            throw new ServiceException("学习记录不存在");
        }

        boolean success = scormAttemptService.commitData(attemptId);
        
        Map<String, Object> data = new HashMap<>();
        data.put("success", success);
        data.put("error_code", success ? "0" : "101");

        return JsonResponse.data(data);
    }

    /**
     * 终止SCORM会话
     */
    @PostMapping("/attempts/{attemptId}/terminate")
    public JsonResponse terminate(@PathVariable Integer attemptId,
                                 @RequestParam(required = false, defaultValue = "1") Integer userId) throws ServiceException {

        ScormAttempt attempt = scormAttemptService.getById(attemptId);
        if (attempt == null || !attempt.getUserId().equals(userId)) {
            throw new ServiceException("学习记录不存在");
        }

        boolean success = scormAttemptService.terminateSession(attemptId);
        
        Map<String, Object> data = new HashMap<>();
        data.put("success", success);
        data.put("error_code", success ? "0" : "101");

        return JsonResponse.data(data);
    }

    /**
     * 构建启动URL
     */
    private String buildLaunchUrl(ScormPackage scormPackage, ScormAttempt attempt) {
        String baseUrl = "/api/v1/scorm/packages/" + scormPackage.getId() + "/content/";
        return baseUrl + scormPackage.getLaunchFile() + "?attempt_id=" + attempt.getId();
    }

    /**
     * 获取SCORM数据值
     */
    private String getScormValue(ScormAttempt attempt, String element) {
        switch (element) {
            case "cmi.core.lesson_status":
            case "cmi.completion_status":
                return attempt.getLessonStatus() != null ? attempt.getLessonStatus() : "not attempted";
            case "cmi.core.score.raw":
            case "cmi.score.raw":
                return attempt.getScoreRaw() != null ? attempt.getScoreRaw().toString() : "";
            case "cmi.core.score.max":
            case "cmi.score.max":
                return attempt.getScoreMax() != null ? attempt.getScoreMax().toString() : "";
            case "cmi.core.score.min":
            case "cmi.score.min":
                return attempt.getScoreMin() != null ? attempt.getScoreMin().toString() : "";
            case "cmi.core.student_id":
            case "cmi.learner_id":
                return attempt.getUserId().toString();
            case "cmi.core.student_name":
            case "cmi.learner_name":
                return "Student " + attempt.getUserId(); // TODO: 获取真实姓名
            case "cmi.core.lesson_location":
            case "cmi.location":
                return attempt.getLocation() != null ? attempt.getLocation() : "";
            case "cmi.suspend_data":
                return attempt.getSuspendData() != null ? attempt.getSuspendData() : "";
            case "cmi.core.entry":
            case "cmi.entry":
                return attempt.getEntry() != null ? attempt.getEntry() : "ab-initio";
            case "cmi.mode":
                return attempt.getMode() != null ? attempt.getMode() : "normal";
            case "cmi.core.credit":
            case "cmi.credit":
                return attempt.getCredit() != null ? attempt.getCredit() : "credit";
            case "cmi.success_status":
                return attempt.getSuccessStatus() != null ? attempt.getSuccessStatus() : "unknown";
            case "cmi.progress_measure":
                return attempt.getProgressMeasure() != null ? attempt.getProgressMeasure().toString() : "";
            default:
                return "";
        }
    }

    /**
     * 设置SCORM数据值
     */
    private void setScormValue(ScormAttempt attempt, String element, String value) {
        switch (element) {
            case "cmi.core.lesson_status":
            case "cmi.completion_status":
                attempt.setLessonStatus(value);
                break;
            case "cmi.core.score.raw":
            case "cmi.score.raw":
                if (!value.isEmpty()) {
                    attempt.setScoreRaw(Double.parseDouble(value));
                }
                break;
            case "cmi.core.score.max":
            case "cmi.score.max":
                if (!value.isEmpty()) {
                    attempt.setScoreMax(Double.parseDouble(value));
                }
                break;
            case "cmi.core.score.min":
            case "cmi.score.min":
                if (!value.isEmpty()) {
                    attempt.setScoreMin(Double.parseDouble(value));
                }
                break;
            case "cmi.core.session_time":
            case "cmi.session_time":
                // 转换时间格式为秒数
                Integer sessionTimeSeconds = parseTimeToSeconds(value);
                if (sessionTimeSeconds != null) {
                    attempt.setSessionTime(sessionTimeSeconds);
                }
                break;
            case "cmi.location":
                attempt.setLocation(value);
                break;
            case "cmi.suspend_data":
                attempt.setSuspendData(value);
                break;
            case "cmi.core.exit":
            case "cmi.exit":
                attempt.setExit(value);
                break;
            case "cmi.success_status":
                attempt.setSuccessStatus(value);
                break;
            case "cmi.progress_measure":
                if (!value.isEmpty()) {
                    attempt.setProgressMeasure(Double.parseDouble(value));
                }
                break;
        }
    }

    /**
     * 解析时间字符串为秒数
     * 支持格式：
     * - ISO 8601: PT0H15M30S
     * - SCORM 1.2: 00:15:30 或 0000:15:30.00
     */
    private Integer parseTimeToSeconds(String timeString) {
        if (timeString == null || timeString.isEmpty()) {
            return null;
        }

        try {
            // ISO 8601 格式 (SCORM 2004): PT0H15M30S
            if (timeString.startsWith("PT")) {
                int hours = 0, minutes = 0, seconds = 0;

                String time = timeString.substring(2); // 去掉 "PT"

                if (time.contains("H")) {
                    String[] parts = time.split("H");
                    hours = Integer.parseInt(parts[0]);
                    time = parts[1];
                }

                if (time.contains("M")) {
                    String[] parts = time.split("M");
                    minutes = Integer.parseInt(parts[0]);
                    time = parts[1];
                }

                if (time.contains("S")) {
                    String secondsPart = time.replace("S", "");
                    if (!secondsPart.isEmpty()) {
                        seconds = (int) Double.parseDouble(secondsPart);
                    }
                }

                return hours * 3600 + minutes * 60 + seconds;
            }

            // SCORM 1.2 格式: 00:15:30 或 0000:15:30.00
            if (timeString.contains(":")) {
                String[] parts = timeString.split(":");
                if (parts.length >= 3) {
                    int hours = Integer.parseInt(parts[0]);
                    int minutes = Integer.parseInt(parts[1]);
                    double secondsDouble = Double.parseDouble(parts[2]);
                    int seconds = (int) secondsDouble;

                    return hours * 3600 + minutes * 60 + seconds;
                }
            }

            // 直接是秒数
            return Integer.parseInt(timeString);

        } catch (Exception e) {
            log.warn("无法解析时间格式: {}", timeString);
            return null;
        }
    }
}
