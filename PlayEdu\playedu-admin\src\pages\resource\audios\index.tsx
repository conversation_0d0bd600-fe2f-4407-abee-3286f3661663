import { useEffect, useState } from "react";
import {
  Spin,
  Button,
  Row,
  Col,
  Modal,
  Empty,
  message,
  Pagination,
  Space,
  Typography,
  Card,
} from "antd";
import { resource } from "../../../api";
import { useLocation } from "react-router-dom";
import styles from "./index.module.less";
import { UploadAudioSub } from "../../../compenents/upload-audio-button/upload-audio-sub";
import { TreeCategory } from "../../../compenents";
import { 
  ExclamationCircleFilled, 
  CheckOutlined, 
  PlayCircleOutlined,
  PauseCircleOutlined,
  SoundOutlined,
  DeleteOutlined,
  EditOutlined,
} from "@ant-design/icons";

const { confirm } = Modal;
const { Text, Title } = Typography;

interface AudioItem {
  id: number;
  category_id: number;
  name: string;
  extension: string;
  size: number;
  disk: string;
  file_id: string;
  path: string;
  url: string;
  created_at: string;
  duration?: number;
}

interface ResourceUrlModel {
  [key: number]: string;
}

const ResourceAudiosPage = () => {
  const result = new URLSearchParams(useLocation().search);
  const [audioList, setAudioList] = useState<AudioItem[]>([]);
  const [resourceUrl, setResourceUrl] = useState<ResourceUrlModel>({});
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(16);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [category_ids, setCategoryIds] = useState<number[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [multiConfig, setMultiConfig] = useState(false);
  const [selLabel, setLabel] = useState<string>(
    result.get("label") ? String(result.get("label")) : "全部音频"
  );
  const [cateId, setCateId] = useState(Number(result.get("cid")));
  const [updateId, setUpdateId] = useState(0);
  const [updateVisible, setUpdateVisible] = useState(false);
  const [playingId, setPlayingId] = useState<number | null>(null);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);

  useEffect(() => {
    setCateId(Number(result.get("cid")));
    if (Number(result.get("cid"))) {
      let arr = [];
      arr.push(Number(result.get("cid")));
      setCategoryIds(arr);
    }
  }, [result.get("cid")]);

  useEffect(() => {
    getAudioList();
  }, [category_ids, refresh, page, size]);

  const getAudioList = () => {
    setLoading(true);
    let categoryIds = category_ids.join(",");
    resource
      .resourceList(page, size, "", "", "", "AUDIO", categoryIds)
      .then((res: any) => {
        setTotal(res.data.result.total);
        setAudioList(res.data.result.data);
        setResourceUrl(res.data.resource_url);
        setLoading(false);
      })
      .catch((err: any) => {
        setLoading(false);
        console.log("错误,", err);
      });
  };

  const resetAudioList = () => {
    setPage(1);
    setAudioList([]);
    setSelectKey([]);
    setRefresh(!refresh);
  };

  const setSelectKey = (keys: any) => {
    setSelectedRowKeys(keys);
    if (keys.length > 0) {
      setMultiConfig(true);
    } else {
      setMultiConfig(false);
    }
  };

  const delMulti = () => {
    if (selectedRowKeys.length === 0) {
      message.error("请选择需要操作的数据");
      return;
    }

    confirm({
      title: "操作确认",
      icon: <ExclamationCircleFilled />,
      content: "确认删除选中的音频？",
      centered: true,
      okText: "确认",
      cancelText: "取消",
      onOk() {
        if (loading) {
          return;
        }
        setLoading(true);
        resource
          .destroyMulti({
            ids: selectedRowKeys,
          })
          .then(() => {
            setLoading(false);
            message.success("成功");
            resetAudioList();
          })
          .catch((e) => {
            setLoading(false);
          });
      },
      onCancel() {
        console.log("Cancel");
      },
    });
  };

  const playAudio = (item: AudioItem) => {
    // 停止当前播放的音频
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
    }

    if (playingId === item.id) {
      // 如果点击的是正在播放的音频，则停止播放
      setPlayingId(null);
      setCurrentAudio(null);
    } else {
      // 播放新的音频
      const audio = new Audio(resourceUrl[item.id]);
      audio.play();
      setPlayingId(item.id);
      setCurrentAudio(audio);

      audio.onended = () => {
        setPlayingId(null);
        setCurrentAudio(null);
      };

      audio.onerror = () => {
        message.error("音频播放失败");
        setPlayingId(null);
        setCurrentAudio(null);
      };
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    if (!seconds) return "未知";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  return (
    <>
      <Row gutter={16}>
        <Col span={5}>
          <TreeCategory
            type="资源"
            text="音频分类"
            refresh={refresh}
            category_ids={category_ids}
            onUpdate={(keys: any, title: any) => {
              setCategoryIds(keys);
              if (typeof title === "string") {
                setLabel(title);
              }
            }}
          />
        </Col>
        <Col span={19}>
          <div className="playedu-main-body">
            <div className="playedu-main-title">音频库</div>
            <div className="playedu-main-title-tip">
              当前分类：{selLabel}
            </div>
            <div className="playedu-main-top mb-30">
              <div className="playedu-main-top-left">
                <UploadAudioSub
                  categoryIds={category_ids}
                  onUpdate={() => {
                    resetAudioList();
                  }}
                />
                {multiConfig && (
                  <Button
                    type="primary"
                    danger
                    onClick={() => delMulti()}
                    className="ml-10"
                  >
                    批量删除
                  </Button>
                )}
              </div>
            </div>
            <div className="playedu-main-top">
              {loading && (
                <div
                  style={{
                    width: "100%",
                    textAlign: "center",
                    paddingTop: 50,
                    paddingBottom: 30,
                  }}
                >
                  <Spin size="large" />
                </div>
              )}
              {!loading && audioList.length === 0 && (
                <Col span={24}>
                  <Empty description="暂无音频" />
                </Col>
              )}
              {!loading && audioList.length > 0 && (
                <div className={styles["audio-grid"]}>
                  {audioList.map((item: AudioItem) => (
                    <Card
                      key={item.id}
                      className={styles["audio-card"]}
                      hoverable
                      actions={[
                        <Button
                          type="text"
                          icon={
                            playingId === item.id ? (
                              <PauseCircleOutlined />
                            ) : (
                              <PlayCircleOutlined />
                            )
                          }
                          onClick={() => playAudio(item)}
                        />,
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          onClick={() => {
                            setUpdateId(item.id);
                            setUpdateVisible(true);
                          }}
                        />,
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => {
                            confirm({
                              title: "操作确认",
                              icon: <ExclamationCircleFilled />,
                              content: "确认删除此音频？",
                              centered: true,
                              okText: "确认",
                              cancelText: "取消",
                              onOk() {
                                resource.destroy(item.id).then(() => {
                                  message.success("删除成功");
                                  resetAudioList();
                                });
                              },
                            });
                          }}
                        />,
                      ]}
                    >
                      <div className={styles["audio-info"]}>
                        <div className={styles["audio-icon"]}>
                          <SoundOutlined style={{ fontSize: "32px", color: "#1890ff" }} />
                        </div>
                        <div className={styles["audio-details"]}>
                          <Title level={5} ellipsis={{ tooltip: item.name }}>
                            {item.name}
                          </Title>
                          <Space direction="vertical" size="small">
                            <Text type="secondary">
                              格式: {item.extension.toUpperCase()}
                            </Text>
                            <Text type="secondary">
                              大小: {formatFileSize(item.size)}
                            </Text>
                            <Text type="secondary">
                              时长: {formatDuration(item.duration || 0)}
                            </Text>
                            <Text type="secondary">
                              上传时间: {item.created_at}
                            </Text>
                          </Space>
                        </div>
                      </div>
                      <div className={styles["audio-select"]}>
                        <Button
                          type={selectedRowKeys.includes(item.id) ? "primary" : "default"}
                          icon={selectedRowKeys.includes(item.id) ? <CheckOutlined /> : null}
                          onClick={() => {
                            if (selectedRowKeys.includes(item.id)) {
                              setSelectKey(selectedRowKeys.filter((key: number) => key !== item.id));
                            } else {
                              setSelectKey([...selectedRowKeys, item.id]);
                            }
                          }}
                        >
                          {selectedRowKeys.includes(item.id) ? "已选" : "选择"}
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
              {!loading && audioList.length > 0 && (
                <Col
                  span={24}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    marginTop: 30,
                  }}
                >
                  <Pagination
                    current={page}
                    total={total}
                    pageSize={size}
                    onChange={(currentPage, currentSize) => {
                      setPage(currentPage);
                      setSize(currentSize || 16);
                    }}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total) => `共 ${total} 个音频`}
                  />
                </Col>
              )}
            </div>
          </div>
        </Col>
      </Row>
    </>
  );
};

export default ResourceAudiosPage;
