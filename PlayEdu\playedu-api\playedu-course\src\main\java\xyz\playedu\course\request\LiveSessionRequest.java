/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.course.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * 直播场次请求类
 * 
 * <AUTHOR> Team
 */
@Data
public class LiveSessionRequest {

    @NotBlank(message = "直播标题不能为空")
    private String title;

    private String description;

    private Integer thumb;

    @NotNull(message = "开始时间不能为空")
    @JsonProperty("start_time")
    private Date startTime;

    @NotNull(message = "结束时间不能为空")
    @JsonProperty("end_time")
    private Date endTime;

    @NotNull(message = "最大观看人数不能为空")
    @JsonProperty("max_viewers")
    private Integer maxViewers;

    @NotNull(message = "是否录制不能为空")
    @JsonProperty("is_record")
    private Integer isRecord;
}
